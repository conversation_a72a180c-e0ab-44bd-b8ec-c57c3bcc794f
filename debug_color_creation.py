#!/usr/bin/env python3

from coloraide import Color

# Test color creation with different hue values
c1 = Color("lch(75% 50 15)")
c2 = Color("lch(75% 50 375)")
c3 = Color("lch(30% 30 375)")

print(f"lch(75% 50 15): {c1}")
print(f"lch(75% 50 375): {c2}")
print(f"lch(30% 30 375): {c3}")

print(f"c1 hue: {c1['hue']}")
print(f"c2 hue: {c2['hue']}")
print(f"c3 hue: {c3['hue']}")

print(f"c1 == c2: {c1 == c2}")

# Test if normalization happens in color space conversion
c4 = Color("srgb", [1, 0, 0]).convert("lch")
print(f"Red in LCH: {c4}")
