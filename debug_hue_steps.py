#!/usr/bin/env python3

from coloraide import Color

# Test step by step
c1 = Color('lch(75% 50 40)')
c2_masked = Color('lch(30% 30 350)').mask("hue", invert=True)

print(f"c1: {c1}")
print(f"c2_masked: {c2_masked}")

# Manual interpolation calculation
# After hue adjustment: c1=40+360=400, c2=350
# At t=0.5: 400 * 0.5 + 350 * 0.5 = 375

# Create the result manually
manual_result = Color('lch', [75, 50, 375])
print(f"Manual result (375°): {manual_result}")

# Normalize manually
manual_result_norm = Color('lch', [75, 50, 375 % 360])
print(f"Manual result normalized (15°): {manual_result_norm}")

# Test actual interpolation
result = c1.mix(c2_masked, 0.50, hue="shorter", space="lch")
print(f"Actual result: {result}")
