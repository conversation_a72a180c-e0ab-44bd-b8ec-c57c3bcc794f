diff --git a/coloraide/color.py b/coloraide/color.py
index 18644c3d..512b58e0 100644
--- a/coloraide/color.py
+++ b/coloraide/color.py
@@ -31,112 +31,117 @@ from .spaces.a98_rgb import A98RGB
 from .spaces.a98_rgb_linear import A98RGBLinear
 from .spaces.prophoto_rgb import ProPhotoRGB
 from .spaces.prophoto_rgb_linear import ProPhotoRGBLinear
 from .spaces.rec2020 import Rec2020
 from .spaces.rec2020_linear import Rec2020Linear
 from .spaces.xyz_d65 import XYZD65
 from .spaces.xyz_d50 import XYZD50
 from .spaces.oklab.css import Oklab
 from .spaces.oklch.css import OkLCh
 from .distance import DeltaE
 from .distance.delta_e_76 import DE76
 from .distance.delta_e_94 import DE94
 from .distance.delta_e_cmc import DECMC
 from .distance.delta_e_2000 import DE2000
 from .distance.delta_e_hyab import DEHyAB
 from .distance.delta_e_ok import DEOK
 from .contrast import ColorContrast
 from .contrast.wcag21 import WCAG21Contrast
 from .gamut import Fit
 from .gamut.fit_lch_chroma import LChChroma
 from .gamut.fit_oklch_chroma import OkLChChroma
 from .cat import CAT, Bradford
 from .filters import Filter
 from .filters.w3c_filter_effects import Sepia, Brightness, Contrast, Saturate, Opacity, HueRotate, Grayscale, Invert
 from .filters.cvd import Protan, Deutan, Tritan
+from .interpolate import Interpolator, Interpolate
+from .interpolate.bezier import InterpolateBezier
+from .interpolate.piecewise import InterpolatePiecewise
 from .types import Plugin
 from typing import overload, Union, Sequence, Dict, List, Optional, Any, cast, Callable, Tuple, Type, Mapping


 class ColorMatch:
     """Color match object."""

     def __init__(self, color: 'Color', start: int, end: int) -> None:
         """Initialize."""

         self.color = color
         self.start = start
         self.end = end

     def __str__(self) -> str:  # pragma: no cover
         """String."""

         return "ColorMatch(color={!r}, start={}, end={})".format(self.color, self.start, self.end)

     __repr__ = __str__


 class ColorMeta(abc.ABCMeta):
     """Ensure on subclass that the subclass has new instances of mappings."""

     def __init__(cls, name: str, bases: Tuple[object, ...], clsdict: Dict[str, Any]) -> None:
         """Copy mappings on subclass."""

         # Ensure subclassed Color objects do not use the same plugin mappings
         if len(cls.mro()) > 2:
             cls.CS_MAP = cls.CS_MAP.copy()  # type: Dict[str, Space]
             cls.DE_MAP = cls.DE_MAP.copy()  # type: Dict[str, DeltaE]
             cls.FIT_MAP = cls.FIT_MAP.copy()  # type: Dict[str, Fit]
             cls.CAT_MAP = cls.CAT_MAP.copy()  # type: Dict[str, CAT]
             cls.FILTER_MAP = cls.FILTER_MAP.copy()  # type: Dict[str, Filter]
             cls.CONTRAST_MAP = cls.CONTRAST_MAP.copy()  # type: Dict[str, ColorContrast]
+            cls.INTERPOLATE_MAP = cls.INTERPOLATE_MAP.copy()  # type: Dict[str, Interpolate]

         # Ensure each derived class tracks its own conversion paths for color spaces
         # relative to the installed color space plugins.
         @classmethod  # type: ignore[misc]
         @functools.lru_cache(maxsize=256)
         def _get_convert_chain(
             cls: Type['Color'],
             space: 'Space',
             target: str
         ) -> List[Tuple['Space', 'Space', int, bool]]:
             """Resolve a conversion chain, cache it for speed."""

             return convert.get_convert_chain(cls, space, target)

         cls._get_convert_chain = _get_convert_chain


 class Color(metaclass=ColorMeta):
     """Color class object which provides access and manipulation of color spaces."""

     CS_MAP = {}  # type: Dict[str, Space]
     DE_MAP = {}  # type: Dict[str, DeltaE]
     FIT_MAP = {}  # type: Dict[str, Fit]
     CAT_MAP = {}  # type: Dict[str, CAT]
     CONTRAST_MAP = {}  # type: Dict[str, ColorContrast]
     FILTER_MAP = {}  # type: Dict[str, Filter]
+    INTERPOLATE_MAP = {}  # type: Dict[str, Interpolate]
     PRECISION = util.DEF_PREC
     FIT = util.DEF_FIT
     INTERPOLATE = util.DEF_INTERPOLATE
     DELTA_E = util.DEF_DELTA_E
     HARMONY = util.DEF_HARMONY
     CHROMATIC_ADAPTATION = 'bradford'
     CONTRAST = 'wcag21'

     # It is highly unlikely that a user would ever need to override this, but
     # just in case, it is exposed, but undocumented.
     #
     # This is meant to prevent infinite loops in the event that a user registers
     # poorly crafted color spaces with circular convert linkage or somehow doesn't
     # resolve to XYZ. 10 is a generous size as our current largest iteration chain
     # is 6, and increasing that past 10 seems highly unlikely:
     #    XYZ -> sRGB Linear -> sRGB -> HSL -> HSV -> HWB
     _MAX_CONVERT_ITERATIONS = 10

     def __init__(
         self,
         color: ColorInput,
         data: Optional[VectorLike] = None,
         alpha: float = util.DEF_ALPHA,
         **kwargs: Any
     ) -> None:
@@ -296,107 +301,112 @@ class Color(metaclass=ColorMeta):
         plugin: Union[Plugin, Sequence[Plugin]],
         *,
         overwrite: bool = False,
         silent: bool = False
     ) -> None:
         """Register the hook."""

         reset_convert_cache = False

         if not isinstance(plugin, Sequence):
             plugin = [plugin]

         mapping = None  # type: Optional[Dict[str, Any]]
         for p in plugin:
             if isinstance(p, Space):
                 mapping = cls.CS_MAP
                 reset_convert_cache = True
             elif isinstance(p, DeltaE):
                 mapping = cls.DE_MAP
             elif isinstance(p, CAT):
                 mapping = cls.CAT_MAP
             elif isinstance(p, Filter):
                 mapping = cls.FILTER_MAP
             elif isinstance(p, ColorContrast):
                 mapping = cls.CONTRAST_MAP
+            elif isinstance(p, Interpolate):
+                mapping = cls.INTERPOLATE_MAP
             elif isinstance(p, Fit):
                 mapping = cls.FIT_MAP
                 if p.NAME == 'clip':
                     if reset_convert_cache:  # pragma: no cover
                         cls._get_convert_chain.cache_clear()
                     if not silent:
                         raise ValueError("'{}' is a reserved name for gamut mapping/reduction and cannot be overridden")
                     continue  # pragma: no cover
             else:
                 if reset_convert_cache:  # pragma: no cover
                     cls._get_convert_chain.cache_clear()
                 raise TypeError("Cannot register plugin of type '{}'".format(type(p)))

             name = p.NAME
             value = p

             if name != "*" and name not in mapping or overwrite:
                 cast(Dict[str, Plugin], mapping)[name] = value
             elif not silent:
                 if reset_convert_cache:  # pragma: no cover
                     cls._get_convert_chain.cache_clear()
                 raise ValueError("A plugin with the name of '{}' already exists or is not allowed".format(name))

         if reset_convert_cache:
             cls._get_convert_chain.cache_clear()

     @classmethod
     def deregister(cls, plugin: Union[str, Sequence[str]], *, silent: bool = False) -> None:
         """Deregister a plugin by name of specified plugin type."""

         reset_convert_cache = False

         if isinstance(plugin, str):
             plugin = [plugin]

         mapping = None  # type: Optional[Dict[str, Any]]
         for p in plugin:
             if p == '*':
                 cls.CS_MAP.clear()
                 cls.DE_MAP.clear()
                 cls.FIT_MAP.clear()
                 cls.CAT_MAP.clear()
                 cls.CONTRAST_MAP.clear()
+                cls.INTERPOLATE_MAP.clear()
                 return

             ptype, name = p.split(':', 1)
             if ptype == 'space':
                 mapping = cls.CS_MAP
                 reset_convert_cache = True
             elif ptype == "delta-e":
                 mapping = cls.DE_MAP
             elif ptype == 'cat':
                 mapping = cls.CAT_MAP
             elif ptype == 'filter':
                 mapping = cls.FILTER_MAP
             elif ptype == 'contrast':
                 mapping = cls.CONTRAST_MAP
+            elif ptype == 'interpolate':
+                mapping = cls.INTERPOLATE_MAP
             elif ptype == "fit":
                 mapping = cls.FIT_MAP
                 if name == 'clip':
                     if reset_convert_cache:  # pragma: no cover
                         cls._get_convert_chain.cache_clear()
                     if not silent:
                         raise ValueError("'{}' is a reserved name gamut mapping/reduction and cannot be removed")
                     continue  # pragma: no cover
             else:
                 if reset_convert_cache:  # pragma: no cover
                     cls._get_convert_chain.cache_clear()
                 raise ValueError("The plugin category of '{}' is not recognized".format(ptype))

             if name == '*':
                 mapping.clear()
             elif name in mapping:
                 del mapping[name]
             elif not silent:
                 if reset_convert_cache:
                     cls._get_convert_chain.cache_clear()
                 raise ValueError("A plugin of name '{}' under category '{}' could not be found".format(name, ptype))

         if reset_convert_cache:
             cls._get_convert_chain.cache_clear()

@@ -682,94 +692,97 @@ class Color(metaclass=ColorMeta):
         return this

     def mix(
         self,
         color: ColorInput,
         percent: float = util.DEF_MIX,
         *,
         in_place: bool = False,
         **interpolate_args: Any
     ) -> 'Color':
         """
         Mix colors using interpolation.

         This uses the interpolate method to find the center point between the two colors.
         The basic mixing logic is outlined in the CSS level 5 draft.
         """

         if not self._is_color(color) and not isinstance(color, (str, Mapping)):
             raise TypeError("Unexpected type '{}'".format(type(color)))
         mixed = self.interpolate([self, color], **interpolate_args)(percent)
         return self.mutate(mixed) if in_place else mixed

     @classmethod
     def steps(
         cls,
-        colors: Sequence[Union[ColorInput, interpolate.common.stop, Callable[..., float]]],
+        colors: Sequence[Union[ColorInput, interpolate.stop, Callable[..., float]]],
         *,
         steps: int = 2,
         max_steps: int = 1000,
         max_delta_e: float = 0,
         delta_e: Optional[str] = None,
         **interpolate_args: Any
     ) -> List['Color']:
         """Discrete steps."""

         return cls.interpolate(colors, **interpolate_args).steps(steps, max_steps, max_delta_e, delta_e)

     @classmethod
     def interpolate(
         cls,
-        colors: Sequence[Union[ColorInput, interpolate.common.stop, Callable[..., float]]],
+        colors: Sequence[Union[ColorInput, interpolate.stop, Callable[..., float]]],
         *,
         space: Optional[str] = None,
         out_space: Optional[str] = None,
         progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]] = None,
         hue: str = util.DEF_HUE_ADJ,
         premultiplied: bool = True,
-        method: str = "linear"
-    ) -> interpolate.common.Interpolator:
+        method: str = "linear",
+        **kwargs: Any
+    ) -> Interpolator:
         """
         Return an interpolation function.

         The function will return an interpolation function that accepts a value (which should
         be in the range of [0..1] and will return a color based on that value.

         While we use NaNs to mask off channels when doing the interpolation, we do not allow
         arbitrary specification of NaNs by the user, they must specify channels via `adjust`
         if they which to target specific channels for mixing. Null hues become NaNs before
         mixing occurs.
         """

-        return interpolate.get_interpolator(method)(
+        return interpolate.get_interpolator(
+            method,
             cls,
             colors=colors,
             space=space,
             out_space=out_space,
             progress=progress,
             hue=hue,
-            premultiplied=premultiplied
+            premultiplied=premultiplied,
+            **kwargs
         )

     def filter(  # noqa: A003
         self,
         name: str,
         amount: Optional[float] = None,
         *,
         space: Optional[str] = None,
         in_place: bool = False,
         **kwargs: Any
     ) -> 'Color':
         """Filter."""

         return filters.filters(self, name, amount, space, in_place, **kwargs)

     def harmony(
         self,
         name: str,
         *,
         space: Optional[str] = None
     ) -> List['Color']:
         """Acquire the specified color harmonies."""

         return harmonies.harmonize(self, name, space)

@@ -905,28 +918,32 @@ Color.register(
         DE76(),
         DE94(),
         DECMC(),
         DE2000(),
         DEHyAB(),
         DEOK(),

         # Fit
         LChChroma(),
         OkLChChroma(),

         # Filters
         Sepia(),
         Brightness(),
         Contrast(),
         Saturate(),
         Opacity(),
         HueRotate(),
         Grayscale(),
         Invert(),
         Protan(),
         Deutan(),
         Tritan(),

         # Contrast
-        WCAG21Contrast()
+        WCAG21Contrast(),
+
+        # Interpolation
+        InterpolateBezier(),
+        InterpolatePiecewise()
     ]
 )
diff --git a/coloraide/harmonies.py b/coloraide/harmonies.py
index f483181f..bb28308a 100644
--- a/coloraide/harmonies.py
+++ b/coloraide/harmonies.py
@@ -53,67 +53,69 @@ class Monochromatic(Harmony):
         if not isinstance(color0._space, Cylindrical):
             raise ValueError('Color space must be cylindrical')

         # Trim off black and white unless the color is achromatic,
         # But always trim duplicate target color from left side.
         if not color0.is_nan('hue'):
             ltrim, rtrim = slice(1, -1, None), slice(None, -1, None)
         else:
             ltrim, rtrim = slice(None, -1, None), slice(None, None, None)

         # Create black and white so we can generate tints and shades
         # Ensure hue and alpha is masked so we don't interpolate them.
         w = color.new('color(srgb 1 1 1 / none)').convert(space, in_place=True).mask(['hue', 'alpha'], in_place=True)
         b = color.new('color(srgb 0 0 0 / none)').convert(space, in_place=True).mask(['hue', 'alpha'], in_place=True)

         # Calculate how many tints and shades we need to generate
         db = b.delta_e(color0, method=self.DELTA_E)
         dw = w.delta_e(color0, method=self.DELTA_E)
         steps_w = int(alg.round_half_up((dw / (db + dw)) * self.RANGE))
         steps_b = self.RANGE - steps_w

         # Very close to black or is black, no need to interpolate from black to current color
         if steps_b <= 1:
             left = []
             if steps_b == 1:
-                left.extend(color.steps([b, color], steps=steps_b, space=space, out_space=orig_space))
+                left.extend(color.steps([b, color], steps=steps_b, space=space, out_space=orig_space, method='linear'))
             steps = min(self.RANGE - (1 + steps_b), steps_w)
-            right = color.steps([color0, w], steps=steps, space=space, out_space=orig_space)[rtrim]
+            right = color.steps([color0, w], steps=steps, space=space, out_space=orig_space, method='linear')[rtrim]

         # Very close to white or is white, no need to interpolate from current color to white
         elif steps_w <= 1:
             right = []
             if steps_w == 1:
-                right.extend(color.steps([color0, w], steps=steps_w, space=space, out_space=orig_space))
+                right.extend(
+                    color.steps([color0, w], steps=steps_w, space=space, out_space=orig_space, method='linear')
+                )
             steps = min(self.RANGE - (1 + steps_w), steps_b)
             right.insert(0, color.clone())
-            left = color.steps([b, color], steps=steps, space=space, out_space=orig_space)[ltrim]
+            left = color.steps([b, color], steps=steps, space=space, out_space=orig_space, method='linear')[ltrim]

         else:
             # Anything else in between
-            left = color.steps([b, color], steps=steps_b, space=space, out_space=orig_space)[ltrim]
-            right = color.steps([color0, w], steps=steps_w, space=space, out_space=orig_space)[rtrim]
+            left = color.steps([b, color], steps=steps_b, space=space, out_space=orig_space, method='linear')[ltrim]
+            right = color.steps([color0, w], steps=steps_w, space=space, out_space=orig_space, method='linear')[rtrim]

         # Extract a subset of the results
         len_l = len(left)
         len_r = len(right)
         l = int(self.STEPS // 2)
         r = l + (1 if self.STEPS % 2 else 0)
         if len_r < r:
             return left[-self.STEPS + len_r:] + right
         elif len_l < l:
             return left + right[:self.STEPS - len_l]
         return left[-l:] + right[:r]


 class Geometric(Harmony):
     """Geometrically space the colors."""

     COUNT = 0

     def harmonize(self, color: 'Color', space: Optional[str]) -> List['Color']:
         """Get color harmonies."""

         if space is None:
             space = color.HARMONY

         orig_space = color.space()
diff --git a/coloraide/interpolate/__init__.py b/coloraide/interpolate/__init__.py
index 0d9c3836..e5855692 100644
--- a/coloraide/interpolate/__init__.py
+++ b/coloraide/interpolate/__init__.py
@@ -1,36 +1,562 @@
 """
 Interpolation methods.

 Originally, the base code for `interpolate`, `mix` and `steps` was ported from the
 https://colorjs.io project. Since that time, there has been significant modifications
 that add additional features etc. The base logic though is attributed to the original
 authors.

 In general, the logic mimics in many ways the `color-mix` function as outlined in the Level 5
 color draft (Oct 2020), but the initial approach was modeled directly off of the work done in
 color.js.
 ---
 Original Authors: <AUTHORS>
 License: MIT (As noted in https://github.com/LeaVerou/color.js/blob/master/package.json)
 """
-from .bezier import color_bezier_lerp
-from .piecewise import color_piecewise_lerp
-from .common import Interpolator, hint, stop  # noqa: F401
-from typing import Callable, Dict
+import math
+import functools
+from abc import ABCMeta, abstractmethod
+from .. import algebra as alg
+from ..spaces import Cylindrical
+from ..channels import FLG_ANGLE
+from ..types import Vector, ColorInput, Plugin
+from typing import Callable, Dict, Tuple, Optional, Type, Sequence, Union, Mapping, List, Any, cast, TYPE_CHECKING
+
+if TYPE_CHECKING:  # pragma: no cover
+    from ..color import Color

 __all__ = ('stop', 'hint', 'get_interpolator')


-SUPPORTED = {
-    "linear": color_piecewise_lerp,
-    "bezier": color_bezier_lerp
-}  # type: Dict[str, Callable[..., Interpolator]]
+class stop:
+    """Color stop."""
+
+    __slots__ = ('color', 'stop')
+
+    def __init__(self, color: ColorInput, value: float) -> None:
+        """Color stops."""
+
+        self.color = color
+        self.stop = value
+
+
+def midpoint(t: float, h: float = 0.5) -> float:
+    """Midpoint easing function."""
+
+    return 0.0 if h <= 0 or h >= 1 else math.pow(t, math.log(0.5) / math.log(h))
+
+
+def hint(mid: float) -> Callable[..., float]:
+    """A generate a midpoint easing function."""
+
+    return functools.partial(midpoint, h=mid)
+
+
+class Interpolator(metaclass=ABCMeta):
+    """Interpolator."""
+
+    def __init__(
+        self,
+        coordinates: List[Vector],
+        names: Sequence[str],
+        create: Type['Color'],
+        easings: List[Optional[Callable[..., float]]],
+        stops: Dict[int, float],
+        space: str,
+        out_space: str,
+        progress: Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]],
+        premultiplied: bool,
+        **kwargs: Any
+    ):
+        """Initialize."""
+
+        self.start = stops[0]
+        self.end = stops[len(stops) - 1]
+        self.stops = stops
+        self.easings = easings
+        self.coordinates = coordinates
+        self.length = len(self.coordinates)
+        self.names = names
+        self.create = create
+        self.progress = progress
+        self.space = space
+        self.out_space = out_space
+        cs = self.create.CS_MAP[out_space]
+        if isinstance(cs, Cylindrical):
+            self.hue_name = cast(Cylindrical, cs).hue_name()
+        else:
+            self.hue_name = ''
+        self.premultiplied = premultiplied
+
+    @abstractmethod
+    def interpolate(
+        self,
+        easing: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+        point: float,
+        index: int,
+    ) -> Vector:
+        """Interpolate."""
+
+    def steps(
+        self,
+        steps: int = 2,
+        max_steps: int = 1000,
+        max_delta_e: float = 0,
+        delta_e: Optional[str] = None
+    ) -> List['Color']:
+        """Steps."""
+
+        actual_steps = steps
+
+        # Allocate at least two steps if we are doing a maximum delta E,
+        if max_delta_e != 0 and actual_steps < 2:
+            actual_steps = 2
+
+        # Make sure we don't start out allocating too many colors
+        if max_steps is not None:
+            actual_steps = min(actual_steps, max_steps)
+
+        ret = []
+        if actual_steps == 1:
+            ret = [{"p": 0.5, "color": self(0.5)}]
+        elif actual_steps > 1:
+            step = 1 / (actual_steps - 1)
+            for i in range(actual_steps):
+                p = i * step
+                ret.append({'p': p, 'color': self(p)})
+
+        # Iterate over all the stops inserting stops in between all colors
+        # if we have any two colors with a max delta greater than what was requested.
+        # We inject between every stop to ensure the midpoint does not shift.
+        if max_delta_e > 0:
+            # Initial check to see if we need to insert more stops
+            m_delta = 0.0
+            for i in range(1, len(ret)):
+                m_delta = max(
+                    m_delta,
+                    cast('Color', ret[i - 1]['color']).delta_e(
+                        cast('Color', ret[i]['color']),
+                        method=delta_e
+                    )
+                )
+
+            # If we currently have delta over our limit inject more stops.
+            # If inserting between every color would push us over the max_steps, halt.
+            total = len(ret)
+            while m_delta > max_delta_e and (total * 2 - 1 <= max_steps):
+                # Inject stops while measuring again to see if it was sufficient
+                m_delta = 0.0
+                i = 1
+                index = 1
+                while index < total:
+                    prev = ret[index - 1]
+                    cur = ret[index]
+                    p = (cast(float, cur['p']) + cast(float, prev['p'])) / 2
+                    color = self(p)
+                    m_delta = max(
+                        m_delta,
+                        color.delta_e(cast('Color', prev['color']), method=delta_e),
+                        color.delta_e(cast('Color', cur['color']), method=delta_e)
+                    )
+                    ret.insert(index, {'p': p, 'color': color})
+                    total += 1
+                    index += 2
+
+        return [cast('Color', i['color']) for i in ret]
+
+    def postdivide(self, color: 'Color') -> None:
+        """Undo premultiplication of semi-transparent colors."""
+
+        alpha = color[-1]
+
+        if alg.is_nan(alpha) or alpha in (0.0, 1.0):
+            return
+
+        channels = color._space.CHANNELS
+        for i, value in enumerate(color[:-1]):
+
+            # Wrap the angle
+            if channels[i].flags & FLG_ANGLE:
+                continue
+            color[i] = value / alpha
+
+    def __call__(self, point: float) -> 'Color':
+        """Interpolate."""
+
+        # Ensure point is within range
+        point = alg.clamp(point, 0.0, 1.0)
+
+        # See if point extends past either the first or last stop
+        if point > self.end:
+            point = self.end
+        elif point < self.start:
+            point = self.start
+
+        # Iterate stops to find where our point falls between
+        last = self.start
+        for i in range(1, self.length):
+            s = self.stops[i]
+            if point <= s:
+
+                # Adjust stop to be relative to the given stops
+                r = s - last
+                adjusted_time = (point - last) / r if r else 1
+
+                # Do we have an easing function between these stops?
+                easing = self.easings[i - 1]  # type: Any
+                if easing is None:
+                    easing = self.progress
+
+                # Interpolate color and return it
+                coords = self.interpolate(easing, adjusted_time, i)
+                color = self.create(self.space, coords[:-1], coords[-1])
+                if self.premultiplied:
+                    self.postdivide(color)
+                if self.out_space != color.space():
+                    color.convert(self.out_space, in_place=True)
+                return color
+            last = s
+
+        # We shouldn't ever hit this, but provided for typing.
+        # If we do hit this, it would be a bug.
+        raise RuntimeError('Iterpolation could not be found for {}'.format(point))  # pragma: no cover
+
+
+class Interpolate(Plugin, metaclass=ABCMeta):
+    """Interpolation plugin."""
+
+    NAME = ""
+
+    @abstractmethod
+    def get_interpolator(self) -> Type[Interpolator]:
+        """Get the interpolator object."""
+
+    def interpolate(
+        self,
+        create: Type['Color'],
+        colors: Sequence[Union[ColorInput, stop, Callable[..., float]]],
+        space: Optional[str],
+        out_space: Optional[str],
+        progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+        hue: str,
+        premultiplied: bool,
+        **kwargs: Any
+    ) -> Interpolator:
+        """Return an interpolator object."""
+
+        return color_interpolator(
+            self.get_interpolator(),
+            create,
+            colors,
+            space,
+            out_space,
+            progress,
+            hue,
+            premultiplied
+        )
+
+
+def calc_stops(stops: Dict[int, float], count: int) -> Dict[int, float]:
+    """Calculate stops."""
+
+    # Ensure the first stop is set to zero if not explicitly set
+    if 0 not in stops or stops[0] is None:
+        stops[0] = 0
+
+    last = stops[0] * 100
+    highest = last
+    empty = None
+    final = {}
+
+    # Build up normalized stops
+    for i in range(count):
+        value = stops.get(i)
+        if value is not None:
+            value *= 100
+
+        # Found an empty hole, track the start
+        if value is None and empty is None:
+            empty = i - 1
+            continue
+        elif value is None:
+            continue
+
+        # We can't have a stop decrease in progression
+        if value < last:
+            value = last
+
+        # Track the largest explicit value set
+        if value > highest:
+            highest = value
+
+        # Fill in hole if one exists.
+        # Holes will be evenly space between the
+        # current and last stop.
+        if empty is not None:
+            r = i - empty
+            increment = (value - last) / r
+            for j in range(empty + 1, i):
+                last += increment
+                final[j] = last / 100
+            empty = None
+
+        # Set the stop and track it as the last
+        last = value
+        final[i] = last / 100

+    # If there is a hole at the end, fill in the hole,
+    # equally spacing the stops from the last to 100%.
+    # If the last is greater than 100%, then all will
+    # be equal to the last.
+    if empty is not None:
+        r = (count - 1) - empty
+        if highest > 100:
+            increment = 0
+        else:
+            increment = (100 - last) / r
+        for j in range(empty + 1, count):
+            last += increment
+            final[j] = last / 100

-def get_interpolator(interpolator: str) -> Callable[..., Interpolator]:
+    return final
+
+
+def process_mapping(
+    progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+    aliases: Mapping[str, str]
+) -> Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]]:
+    """Process a mapping, such that it is not using aliases."""
+
+    if not isinstance(progress, Mapping):
+        return progress
+    return {aliases.get(k, k): v for k, v in progress.items()}
+
+
+def premultiply(color: 'Color') -> None:
+    """Premultiply the given transparent color."""
+
+    alpha = color[-1]
+
+    if alg.is_nan(alpha) or alpha == 1.0:
+        return
+
+    channels = color._space.CHANNELS
+    for i, value in enumerate(color[:-1]):
+
+        # Wrap the angle
+        if channels[i].flags & FLG_ANGLE:
+            continue
+        color[i] = value * alpha
+
+
+def normalize_color(color: 'Color', space: str, premultiplied: bool) -> None:
+    """Normalize color."""
+
+    # Adjust to color to space and ensure it fits
+    if not color.CS_MAP[space].EXTENDED_RANGE:
+        if not color.in_gamut():
+            color.fit()
+
+    # Premultiply
+    if premultiplied:
+        premultiply(color)
+
+
+def adjust_shorter(h1: float, h2: float, offset: float) -> Tuple[float, float]:
+    """Adjust the given hues."""
+
+    d = h2 - h1
+    if d > 180:
+        h2 -= 360.0
+        offset -= 360.0
+    elif d < -180:
+        h2 += 360
+        offset += 360.0
+    return h2, offset
+
+
+def adjust_longer(h1: float, h2: float, offset: float) -> Tuple[float, float]:
+    """Adjust the given hues."""
+
+    d = h2 - h1
+    if 0 < d < 180:
+        h2 -= 360.0
+        offset -= 360.0
+    elif -180 < d <= 0:
+        h2 += 360
+        offset += 360.0
+    return h2, offset
+
+
+def adjust_increase(h1: float, h2: float, offset: float) -> Tuple[float, float]:
+    """Adjust the given hues."""
+
+    if h2 < h1:
+        h2 += 360.0
+        offset += 360.0
+    return h2, offset
+
+
+def adjust_decrease(h1: float, h2: float, offset: float) -> Tuple[float, float]:
+    """Adjust the given hues."""
+
+    if h2 > h1:
+        h2 -= 360.0
+        offset -= 360.0
+    return h2, offset
+
+
+def normalize_hue(
+    color1: Vector,
+    color2: Optional[Vector],
+    index: int,
+    offset: float,
+    hue: str
+) -> Tuple[Vector, float]:
+    """Normalize hues according the hue specifier."""
+
+    if hue == 'specified':
+        return (color2 or color1), offset
+
+    # Probably the first hue
+    if color2 is None:
+        color1[index] = color1[index] % 360
+        return color1, offset
+
+    if hue == 'shorter':
+        adjuster = adjust_shorter
+    elif hue == 'longer':
+        adjuster = adjust_longer
+    elif hue == 'increasing':
+        adjuster = adjust_increase
+    elif hue == 'decreasing':
+        adjuster = adjust_decrease
+    else:
+        raise ValueError("Unknown hue adjuster '{}'".format(hue))
+
+    offset = 0
+    c1 = color1[index]
+    c2 = (color2[index] % 360) + offset
+
+    # Undefined hue, can't resolve
+    if alg.is_nan(c1) or alg.is_nan(c2):
+        color2[index] = c2
+        return color2, offset
+
+    # Adjust hues
+    c2, offset = adjuster(c1, c2, offset)
+    color2[index] = c2
+    return color2, offset
+
+
+def color_interpolator(
+    plugin: Type[Interpolator],
+    create: Type['Color'],
+    colors: Sequence[Union[ColorInput, stop, Callable[..., float]]],
+    space: Optional[str],
+    out_space: Optional[str],
+    progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+    hue: str,
+    premultiplied: bool,
+    **kwargs: Any
+) -> Interpolator:
+    """Bezier interpolation."""
+
+    # Construct piecewise interpolation object
+    stops = {}  # type: Any
+
+    if space is None:
+        space = create.INTERPOLATE
+
+    if isinstance(colors[0], stop):
+        current = create(colors[0].color)
+        stops[0] = colors[0].stop
+    elif not callable(colors[0]):
+        current = create(colors[0])
+        stops[0] = None
+    else:
+        raise ValueError('Cannot have an easing function as the first item in an interpolation list')
+
+    if out_space is None:
+        out_space = current.space()
+
+    current.convert(space, in_place=True)
+    offset = 0.0
+    hue_index = cast(Cylindrical, current._space).hue_index() if isinstance(current._space, Cylindrical) else -1
+    normalize_color(current, space, premultiplied)
+    norm, offset = normalize_hue(current[:], None, hue_index, offset, hue)
+
+    easing = None  # type: Any
+    easings = []  # type: Any
+    coords = [norm]
+
+    i = 0
+    for x in colors[1:]:
+
+        # Normalize all colors as Piecewise objects
+        if isinstance(x, stop):
+            i += 1
+            stops[i] = x.stop
+            color = current._handle_color_input(x.color)
+        elif callable(x):
+            easing = x
+            continue
+        else:
+            i += 1
+            color = current._handle_color_input(x)
+            stops[i] = None
+
+        # Adjust to color to space and ensure it fits
+        color = color.convert(space)
+        normalize_color(color, space, premultiplied)
+        norm, offset = normalize_hue(current[:], color[:], hue_index, offset, hue)
+
+        # Create an entry interpolating the current color and the next color
+        coords.append(norm)
+        easings.append(easing if easing is not None else progress)
+
+        # The "next" color is now the "current" color
+        easing = None
+        current = color
+
+    i += 1
+    if i < 2:
+        raise ValueError('Need at least two colors to interpolate')
+
+    # Calculate stops
+    stops = calc_stops(stops, i)
+
+    # Send the interpolation list along with the stop map to the Piecewise interpolator
+    return plugin(
+        coords,
+        current._space.channels,
+        create,
+        easings,
+        stops,
+        space,
+        out_space,
+        process_mapping(progress, current._space.CHANNEL_ALIASES),
+        premultiplied,
+        **kwargs
+    )
+
+
+def get_interpolator(
+    interpolator: str,
+    create: Type['Color'],
+    colors: Sequence[Union[ColorInput, stop, Callable[..., float]]],
+    space: Optional[str],
+    out_space: Optional[str],
+    progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+    hue: str,
+    premultiplied: bool,
+    **kwargs: Any
+) -> Interpolator:
     """Get desired blend mode."""

     try:
-        return SUPPORTED[interpolator]
+        i = create.INTERPOLATE_MAP[interpolator]
     except KeyError:
         raise ValueError("'{}' is not a recognized interpolator".format(interpolator))
+
+    return i.interpolate(create, colors, space, out_space, progress, hue, premultiplied, **kwargs)
diff --git a/coloraide/interpolate/bezier.py b/coloraide/interpolate/bezier.py
index a0b4e90a..3b82335a 100644
--- a/coloraide/interpolate/bezier.py
+++ b/coloraide/interpolate/bezier.py
@@ -1,248 +1,108 @@
 """Bezier interpolation."""
 from .. import algebra as alg
-from ..spaces import Cylindrical
-from ..types import Vector, ColorInput
-from typing import Optional, Callable, Sequence, Mapping, Type, Dict, List, Union, cast, Any, TYPE_CHECKING
-from .common import stop, Interpolator, calc_stops, process_mapping, premultiply, postdivide
-
-if TYPE_CHECKING:  # pragma: no cover
-    from ..color import Color
+from ..interpolate import Interpolator, Interpolate
+from ..types import Vector
+from typing import Optional, Callable, Mapping, List, Union, Type


 def binomial_row(n: int) -> List[int]:
     """
     Binomial row.

     Return row in Pascal's triangle.
     """

     row = [1, 1]
-    for i in range(n - 1):
+    for _ in range(n - 1):
         r = [1]
         x = 0
         for x in range(1, len(row)):
             r.append(row[x] + row[x - 1])
         r.append(row[x])
         row = r
     return row


-class InterpolateBezier(Interpolator):
-    """Interpolate Bezier."""
+def handle_undefined(coords: Vector) -> Vector:
+    """Handle null values."""
+
+    backfill = None
+    for x in range(1, len(coords)):
+        a = coords[x - 1]
+        b = coords[x]
+        if alg.is_nan(a) and not alg.is_nan(b):
+            coords[x - 1] = b
+        elif alg.is_nan(b) and not alg.is_nan(a):
+            coords[x] = a
+        elif alg.is_nan(a) and alg.is_nan(b):
+            # Multiple undefined values, mark the start
+            backfill = x - 1
+            continue

-    def __init__(
-        self,
-        coordinates: List[Vector],
-        names: Sequence[str],
-        create: Type['Color'],
-        easings: List[Optional[Callable[..., float]]],
-        stops: Dict[int, float],
-        space: str,
-        out_space: str,
-        progress: Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]],
-        premultiplied: bool
-    ) -> None:
-        """Initialize."""
-
-        self.start = stops[0]
-        self.end = stops[len(stops) - 1]
-        self.stops = stops
-        self.easings = easings
-        self.coordinates = coordinates
-        self.length = len(self.coordinates)
-        self.names = names
-        self.create = create
-        self.progress = progress
-        self.space = space
-        self.out_space = out_space
-        self.premultiplied = premultiplied
-
-    def handle_undefined(self, coords: Vector) -> Vector:
-        """Handle null values."""
-
-        backfill = None
-        for x in range(1, len(coords)):
-            a = coords[x - 1]
-            b = coords[x]
-            if alg.is_nan(a) and not alg.is_nan(b):
-                coords[x - 1] = b
-            elif alg.is_nan(b) and not alg.is_nan(a):
-                coords[x] = a
-            elif alg.is_nan(a) and alg.is_nan(b):
-                # Multiple undefined values, mark the start
-                backfill = x - 1
-                continue
-
-            # Replace all undefined values that occurred prior to
-            # finding the current defined value
-            if backfill is not None:
-                coords[backfill:x - 1] = [b] * (x - 1 - backfill)
-                backfill = None
-
-        return coords
+        # Replace all undefined values that occurred prior to
+        # finding the current defined value
+        if backfill is not None:
+            coords[backfill:x - 1] = [b] * (x - 1 - backfill)
+            backfill = None
+
+    return coords
+
+
+class InterpolatorBezier(Interpolator):
+    """Interpolate Bezier."""

     def interpolate(
         self,
         easing: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
-        p2: float,
-        first: float,
-        last: float
-    ) -> 'Color':
+        point: float,
+        index: int
+    ) -> Vector:
         """Interpolate."""

+        # Bezier interpolates against all colors, so make point absolute
+        piece = 1 / (self.length - 1)
+        first = (index - 1) * piece
+        last = index * piece
+
+        # Get row from Pascal's Triangle
         n = self.length - 1
         row = binomial_row(n)
+
+        # Use Bezier interpolation of all color for each channel
         channels = []
         for i, coords in enumerate(zip(*self.coordinates)):
-            name = self.names[i]
+
+            # Do we have an easing function, or mapping with a channel easing function?
             progress = None
+            name = self.names[i]
             if isinstance(easing, Mapping):
                 progress = easing.get(name)
                 if progress is None:
                     progress = easing.get('all')
             else:
                 progress = easing

             # Apply easing and scale properly between the colors
-            t = alg.clamp(p2 if progress is None else progress(p2), 0.0, 1.0)
+            t = alg.clamp(point if progress is None else progress(point), 0.0, 1.0)
             t = t * (last - first) + first

-            # Find new points using a bezier curve
+            # Find new points using a bezier curve, but ensure we handle undefined
+            # values in a sane way.
             x = 1 - t
             s = 0.0
-            for j, c in enumerate(self.handle_undefined(list(coords)), 0):
+            for j, c in enumerate(handle_undefined(list(coords)), 0):
                 s += row[j] * (x ** (n - j)) * (t ** j) * c
-
             channels.append(s)
-        color = self.create(self.space, channels[:-1], channels[-1])
-        if self.premultiplied:
-            postdivide(color)
-        return color.convert(self.out_space, in_place=True) if self.out_space != color.space() else color

-    def __call__(self, p: float) -> 'Color':
-        """Interpolate."""
+        return channels

-        percent = alg.clamp(p, 0.0, 1.0)
-        if percent > self.end:
-            percent = self.end
-        elif percent < self.start:
-            percent = self.start
-        last = self.start
-        for i in range(1, self.length):
-            s = self.stops[i]
-            if percent <= s:
-                r = s - last
-                p2 = (percent - last) / r if r else 1
-                easing = self.easings[i - 1]  # type: Any
-                if easing is None:
-                    easing = self.progress
-                piece = 1 / (self.length - 1)
-                return self.interpolate(easing, p2, (i - 1) * piece, i * piece)
-            last = s
-
-        # We shouldn't ever hit this, but provided for typing.
-        # If we do hit this, it would be a bug.
-        raise RuntimeError('Iterpolation could not be found for {}'.format(percent))  # pragma: no cover
-
-
-def normalize_color(color: 'Color', space: str, premultiplied: bool) -> None:
-    """Normalize color."""
-
-    # Adjust to color to space and ensure it fits
-    if not color.CS_MAP[space].EXTENDED_RANGE:
-        if not color.in_gamut():
-            color.fit()
-
-    # Premultiply
-    if premultiplied:
-        premultiply(color)
-
-    # Normalize hue
-    if isinstance(color._space, Cylindrical):
-        name = cast(Cylindrical, color._space).hue_name()
-        color.set(name, lambda h: cast(float, h % 360))
-
-
-def color_bezier_lerp(
-    create: Type['Color'],
-    colors: List[ColorInput],
-    space: str,
-    out_space: str,
-    progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
-    premultiplied: bool,
-    **kwargs: Any
-) -> InterpolateBezier:
-    """Bezier interpolation."""
-
-    # Construct piecewise interpolation object
-    stops = {}  # type: Any
-
-    if space is None:
-        space = create.INTERPOLATE
-
-    if isinstance(colors[0], stop):
-        current = create(colors[0].color)
-        stops[0] = colors[0].stop
-    elif not callable(colors[0]):
-        current = create(colors[0])
-        stops[0] = None
-    else:
-        raise ValueError('Cannot have an easing function as the first item in an interpolation list')
-
-    if out_space is None:
-        out_space = current.space()
-
-    current.convert(space, in_place=True)
-    normalize_color(current, space, premultiplied)
-
-    easing = None  # type: Any
-    easings = []  # type: Any
-    coords = [current[:]]
-
-    i = 0
-    for x in colors[1:]:
-
-        # Normalize all colors as Piecewise objects
-        if isinstance(x, stop):
-            i += 1
-            stops[i] = x.stop
-            color = current._handle_color_input(x.color)
-        elif callable(x):
-            easing = x
-            continue
-        else:
-            i += 1
-            color = current._handle_color_input(x)
-            stops[i] = None
-
-        # Adjust to color to space and ensure it fits
-        color = color.convert(space)
-        normalize_color(color, space, premultiplied)
-
-        # Create an entry interpolating the current color and the next color
-        coords.append(color[:])
-        easings.append(easing if easing is not None else progress)
-
-        # The "next" color is now the "current" color
-        easing = None
-        current = color
-
-    i += 1
-    if i < 2:
-        raise ValueError('Need at least two colors to interpolate')
-
-    # Calculate stops
-    stops = calc_stops(stops, i)
-
-    # Send the interpolation list along with the stop map to the Piecewise interpolator
-    return InterpolateBezier(
-        coords,
-        current._space.channels,
-        create,
-        easings,
-        stops,
-        space,
-        out_space,
-        process_mapping(progress, current._space.CHANNEL_ALIASES),
-        premultiplied
-    )
+
+class InterpolateBezier(Interpolate):
+    """Bezier interpolation plugin."""
+
+    NAME = "bezier"
+
+    def get_interpolator(self) -> Type[Interpolator]:
+        """Return the Bezier interpolator."""
+
+        return InterpolatorBezier
diff --git a/coloraide/interpolate/common.py b/coloraide/interpolate/common.py
deleted file mode 100644
index 478246e7..00000000
--- a/coloraide/interpolate/common.py
+++ /dev/null
@@ -1,236 +0,0 @@
-"""Common tools."""
-import math
-import functools
-from abc import ABCMeta, abstractmethod
-from .. import algebra as alg
-from ..channels import FLG_ANGLE
-from ..types import ColorInput
-from typing import Optional, Callable, Mapping, Dict, List, Union, cast, TYPE_CHECKING
-
-if TYPE_CHECKING:  # pragma: no cover
-    from ..color import Color
-
-
-def midpoint(t: float, h: float = 0.5) -> float:
-    """Midpoint easing function."""
-
-    return 0.0 if h <= 0 or h >= 1 else math.pow(t, math.log(0.5) / math.log(h))
-
-
-def hint(mid: float) -> Callable[..., float]:
-    """A generate a midpoint easing function."""
-
-    return functools.partial(midpoint, h=mid)
-
-
-class stop:
-    """Color stop."""
-
-    __slots__ = ('color', 'stop')
-
-    def __init__(self, color: ColorInput, value: float) -> None:
-        """Color stops."""
-
-        self.color = color
-        self.stop = value
-
-
-class Interpolator(metaclass=ABCMeta):
-    """Interpolator."""
-
-    @abstractmethod
-    def __init__(self) -> None:
-        """Initialize."""
-
-    @abstractmethod
-    def __call__(self, p: float) -> 'Color':
-        """Call the interpolator."""
-
-    def steps(
-        self,
-        steps: int = 2,
-        max_steps: int = 1000,
-        max_delta_e: float = 0,
-        delta_e: Optional[str] = None
-    ) -> List['Color']:
-        """Steps."""
-
-        return color_steps(self, steps, max_steps, max_delta_e, delta_e)
-
-
-def calc_stops(stops: Dict[int, float], count: int) -> Dict[int, float]:
-    """Calculate stops."""
-
-    # Ensure the first stop is set to zero if not explicitly set
-    if 0 not in stops or stops[0] is None:
-        stops[0] = 0
-
-    last = stops[0] * 100
-    highest = last
-    empty = None
-    final = {}
-
-    # Build up normalized stops
-    for i in range(count):
-        value = stops.get(i)
-        if value is not None:
-            value *= 100
-
-        # Found an empty hole, track the start
-        if value is None and empty is None:
-            empty = i - 1
-            continue
-        elif value is None:
-            continue
-
-        # We can't have a stop decrease in progression
-        if value < last:
-            value = last
-
-        # Track the largest explicit value set
-        if value > highest:
-            highest = value
-
-        # Fill in hole if one exists.
-        # Holes will be evenly space between the
-        # current and last stop.
-        if empty is not None:
-            r = i - empty
-            increment = (value - last) / r
-            for j in range(empty + 1, i):
-                last += increment
-                final[j] = last / 100
-            empty = None
-
-        # Set the stop and track it as the last
-        last = value
-        final[i] = last / 100
-
-    # If there is a hole at the end, fill in the hole,
-    # equally spacing the stops from the last to 100%.
-    # If the last is greater than 100%, then all will
-    # be equal to the last.
-    if empty is not None:
-        r = (count - 1) - empty
-        if highest > 100:
-            increment = 0
-        else:
-            increment = (100 - last) / r
-        for j in range(empty + 1, count):
-            last += increment
-            final[j] = last / 100
-
-    return final
-
-
-def process_mapping(
-    progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
-    aliases: Mapping[str, str]
-) -> Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]]:
-    """Process a mapping, such that it is not using aliases."""
-
-    if not isinstance(progress, Mapping):
-        return progress
-    return {aliases.get(k, k): v for k, v in progress.items()}
-
-
-def postdivide(color: 'Color') -> None:
-    """Premultiply the given transparent color."""
-
-    alpha = color[-1]
-
-    if alg.is_nan(alpha) or alpha in (0.0, 1.0):
-        return
-
-    channels = color._space.CHANNELS
-    for i, value in enumerate(color[:-1]):
-
-        # Wrap the angle
-        if channels[i].flags & FLG_ANGLE:
-            continue
-        color[i] = value / alpha
-
-
-def premultiply(color: 'Color') -> None:
-    """Premultiply the given transparent color."""
-
-    alpha = color[-1]
-
-    if alg.is_nan(alpha) or alpha == 1.0:
-        return
-
-    channels = color._space.CHANNELS
-    for i, value in enumerate(color[:-1]):
-
-        # Wrap the angle
-        if channels[i].flags & FLG_ANGLE:
-            continue
-        color[i] = value * alpha
-
-
-def color_steps(
-    interpolator: Interpolator,
-    steps: int = 2,
-    max_steps: int = 1000,
-    max_delta_e: float = 0,
-    delta_e: Optional[str] = None
-) -> List['Color']:
-    """Color steps."""
-
-    actual_steps = steps
-
-    # Allocate at least two steps if we are doing a maximum delta E,
-    if max_delta_e != 0 and actual_steps < 2:
-        actual_steps = 2
-
-    # Make sure we don't start out allocating too many colors
-    if max_steps is not None:
-        actual_steps = min(actual_steps, max_steps)
-
-    ret = []
-    if actual_steps == 1:
-        ret = [{"p": 0.5, "color": interpolator(0.5)}]
-    elif actual_steps > 1:
-        step = 1 / (actual_steps - 1)
-        for i in range(actual_steps):
-            p = i * step
-            ret.append({'p': p, 'color': interpolator(p)})
-
-    # Iterate over all the stops inserting stops in between all colors
-    # if we have any two colors with a max delta greater than what was requested.
-    # We inject between every stop to ensure the midpoint does not shift.
-    if max_delta_e > 0:
-        # Initial check to see if we need to insert more stops
-        m_delta = 0.0
-        for i in range(1, len(ret)):
-            m_delta = max(
-                m_delta,
-                cast('Color', ret[i - 1]['color']).delta_e(
-                    cast('Color', ret[i]['color']),
-                    method=delta_e
-                )
-            )
-
-        # If we currently have delta over our limit inject more stops.
-        # If inserting between every color would push us over the max_steps, halt.
-        total = len(ret)
-        while m_delta > max_delta_e and (total * 2 - 1 <= max_steps):
-            # Inject stops while measuring again to see if it was sufficient
-            m_delta = 0.0
-            i = 1
-            index = 1
-            while index < total:
-                prev = ret[index - 1]
-                cur = ret[index]
-                p = (cast(float, cur['p']) + cast(float, prev['p'])) / 2
-                color = interpolator(p)
-                m_delta = max(
-                    m_delta,
-                    color.delta_e(cast('Color', prev['color']), method=delta_e),
-                    color.delta_e(cast('Color', cur['color']), method=delta_e)
-                )
-                ret.insert(index, {'p': p, 'color': color})
-                total += 1
-                index += 2
-
-    return [cast('Color', i['color']) for i in ret]
diff --git a/coloraide/interpolate/piecewise.py b/coloraide/interpolate/piecewise.py
index 3bd2b847..7896f09b 100644
--- a/coloraide/interpolate/piecewise.py
+++ b/coloraide/interpolate/piecewise.py
@@ -1,246 +1,64 @@
 """Piecewise linear interpolation."""
 from .. import algebra as alg
-from ..spaces import Cylindrical
-from ..types import Vector, ColorInput
-from typing import Optional, Callable, Sequence, Mapping, Type, Dict, List, Union, cast, Tuple, Any, TYPE_CHECKING
-from .common import stop, Interpolator, calc_stops, process_mapping, premultiply, postdivide
+from ..interpolate import Interpolator, Interpolate
+from ..types import Vector
+from typing import Optional, Callable, Mapping, Union, Type

-if TYPE_CHECKING:  # pragma: no cover
-    from ..color import Color

-
-def adjust_hues(color1: 'Color', color2: 'Color', hue: str) -> None:
-    """Adjust hues."""
-
-    if hue == "specified":
-        return
-
-    name = cast(Cylindrical, color1._space).hue_name()
-    c1 = color1.get(name)
-    c2 = color2.get(name)
-
-    c1 = c1 % 360
-    c2 = c2 % 360
-
-    if alg.is_nan(c1) or alg.is_nan(c2):
-        color1.set(name, c1)
-        color2.set(name, c2)
-        return
-
-    if hue == "shorter":
-        if c2 - c1 > 180:
-            c1 += 360
-        elif c2 - c1 < -180:
-            c2 += 360
-
-    elif hue == "longer":
-        if 0 < (c2 - c1) < 180:
-            c1 += 360
-        elif -180 < (c2 - c1) <= 0:
-            c2 += 360
-
-    elif hue == "increasing":
-        if c2 < c1:
-            c2 += 360
-
-    elif hue == "decreasing":
-        if c1 < c2:
-            c1 += 360
-
-    else:
-        raise ValueError("Unknown hue adjuster '{}'".format(hue))
-
-    color1.set(name, c1)
-    color2.set(name, c2)
-
-
-class InterpolatePiecewise(Interpolator):
+class InterpolatorPiecewise(Interpolator):
     """Interpolate multiple ranges of colors."""

-    def __init__(
-        self,
-        color_map: List[Tuple[Vector, Vector]],
-        names: Sequence[str],
-        create: Type['Color'],
-        easings: List[Optional[Callable[..., float]]],
-        stops: Dict[int, float],
-        space: str,
-        out_space: str,
-        progress: Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]],
-        premultiplied: bool
-    ):
-        """Initialize."""
-
-        self.start = stops[0]
-        self.end = stops[len(stops) - 1]
-        self.stops = stops
-        self.color_map = color_map
-        self.names = names
-        self.create = create
-        self.easings = easings
-        self.space = space
-        self.out_space = out_space
-        self.progress = progress
-        self.premultiplied = premultiplied
-
     def interpolate(
         self,
-        colors: Tuple[Vector, Vector],
         easing: Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]],
-        p: float
-    ) -> 'Color':
+        point: float,
+        index: int
+    ) -> Vector:
         """Interpolate."""

+        # Interpolate between the values of the two colors for each channel.
         channels = []
-        for i, values in enumerate(zip(*colors)):
+        for i, values in enumerate(zip(*self.coordinates[index - 1:index + 1])):
             c1, c2 = values
-            name = self.names[i]
+
+            # Both values are undefined, so return undefined
             if alg.is_nan(c1) and alg.is_nan(c2):
                 value = alg.NaN
+
+            # One channel is undefined, take the one that is not
             elif alg.is_nan(c1):
                 value = c2
             elif alg.is_nan(c2):
                 value = c1
+
+            # Using linear interpolation between the two points
             else:
+                # Do we have an easing function, or mapping with a channel easing function?
                 progress = None
+                name = self.names[i]
                 if isinstance(easing, Mapping):
                     progress = easing.get(name)
                     if progress is None:
                         progress = easing.get('all')
                 else:
                     progress = easing
-                t = alg.clamp(progress(p), 0.0, 1.0) if progress is not None else p
-                value = alg.lerp(c1, c2, t)
-            channels.append(value)
-        color = self.create(self.space, channels[:-1], channels[-1])
-        if self.premultiplied:
-            postdivide(color)
-        return color.convert(self.out_space, in_place=True) if self.out_space != color.space() else color
-
-    def __call__(self, p: float) -> 'Color':
-        """Interpolate."""
-
-        percent = alg.clamp(p, 0.0, 1.0)
-        if percent > self.end:
-            percent = self.end
-        elif percent < self.start:
-            percent = self.start
-        last = self.start
-        for i, colors in enumerate(self.color_map, 1):
-            s = self.stops[i]
-            if percent <= s:
-                r = s - last
-                p2 = (percent - last) / r if r else 1
-                easing = self.easings[i - 1]  # type: Any
-                if easing is None:
-                    easing = self.progress
-                return self.interpolate(colors, easing, p2)
-            last = s
-
-        # We shouldn't ever hit this, but provided for typing.
-        # If we do hit this, it would be a bug.
-        raise RuntimeError('Iterpolation could not be found for {}'.format(percent))  # pragma: no cover
-
-
-def normalize_color(color: 'Color', space: str, premultiplied: bool) -> None:
-    """Normalize the color."""
-
-    # Adjust to color to space and ensure it fits
-    if not color.CS_MAP[space].EXTENDED_RANGE:
-        if not color.in_gamut():
-            color.fit()
-
-    # Premultiply
-    if premultiplied:
-        premultiply(color)
-

-def color_piecewise_lerp(
-    create: Type['Color'],
-    colors: List[Union[ColorInput, stop, Callable[..., float]]],
-    space: str,
-    out_space: str,
-    progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
-    hue: str,
-    premultiplied: bool,
-    **kwargs: Any
-) -> InterpolatePiecewise:
-    """Piecewise Interpolation."""
+                # Apply easing and scale properly between the colors
+                t = alg.clamp(point if progress is None else progress(point), 0.0, 1.0)

-    # Construct piecewise interpolation object
-    stops = {}  # type: Any
-    color_map = []
-
-    if space is None:
-        space = create.INTERPOLATE
-
-    if isinstance(colors[0], stop):
-        current = create(colors[0].color)
-        stops[0] = colors[0].stop
-    elif not callable(colors[0]):
-        current = create(colors[0])
-        stops[0] = None
-    else:
-        raise ValueError('Cannot have an easing function as the first item in an interpolation list')
-
-    if out_space is None:
-        out_space = current.space()
-
-    current.convert(space, in_place=True)
-    normalize_color(current, space, premultiplied)
-
-    easing = None  # type: Any
-    easings = []  # type: Any
-
-    i = 0
-    for x in colors[1:]:
-
-        # Normalize all colors as Piecewise objects
-        if isinstance(x, stop):
-            i += 1
-            stops[i] = x.stop
-            color = current._handle_color_input(x.color)
-        elif callable(x):
-            easing = x
-            continue
-        else:
-            i += 1
-            color = current._handle_color_input(x)
-            stops[i] = None
-
-        # Adjust to color to space and ensure it fits
-        color = color.convert(space)
-        normalize_color(color, space, premultiplied)
+                # Interpolate
+                value = alg.lerp(c1, c2, t)
+            channels.append(value)

-        # Adjust hues if we have two valid hues
-        color2 = color.clone()
-        if isinstance(current._space, Cylindrical):
-            adjust_hues(current, color2, hue)
+        return channels

-        # Create an entry interpolating the current color and the next color
-        color_map.append((current[:], color2[:]))
-        easings.append(easing if easing is not None else progress)

-        # The "next" color is now the "current" color
-        easing = None
-        current = color
+class InterpolatePiecewise(Interpolate):
+    """Linear piecewise interpolation plugin."""

-    i += 1
-    if i < 2:
-        raise ValueError('Need at least two colors to interpolate')
+    NAME = "linear"

-    # Calculate stops
-    stops = calc_stops(stops, i)
+    def get_interpolator(self) -> Type[Interpolator]:
+        """Return the linear piecewise interpolator."""

-    # Send the interpolation list along with the stop map to the Piecewise interpolator
-    return InterpolatePiecewise(
-        color_map,
-        current._space.channels,
-        create,
-        easings,
-        stops,
-        space,
-        out_space,
-        process_mapping(progress, current._space.CHANNEL_ALIASES),
-        premultiplied
-    )
+        return InterpolatorPiecewise
diff --git a/docs/src/markdown/about/changelog.md b/docs/src/markdown/about/changelog.md
index 4c04f31d..85ef4fc3 100644
--- a/docs/src/markdown/about/changelog.md
+++ b/docs/src/markdown/about/changelog.md
@@ -1,27 +1,33 @@
 # Changelog

+## 1.0rc2
+
+- **NEW**: Bezier interpolation now supports hue fix-ups: `shorter`, `longer`, `increasing`, `decreasing`,
+  and `specified`.
+- **NEW**: Interpolation is now exposed as a plugin to allow for expansion.
+
 ## 1.0rc1

 !!! warning "Plugin Refactor"
     For more flexibility there was one final rework of plugins. Registering requires all plugins to be instantiated
     before being passed into `Color.register`, but this allows a user redefine some defaults of certain plugins.

     `coloraide.ColorAll` was moved to `coloraide.everythng.ColorAll` to avoid allocating plugins when they are not
     desired.

     In the process, we also renamed a number of plugin classes for consistency and predictability, details found below.

 - **NEW**: Updated some class names for consistency and predictability. `XyY` --> `xyY`, `Din99o` --> `DIN99o`, `SRGB`
   --> `sRGB`, and `ORGB` --> `oRGB`.

     Lastly, `LCh` should be the default casing convention. This convention will be followed unless a spec mentions
   otherwise. Changes: `Lch` --> `LCh`, `LchD65` --> `LChD65`, `Oklch` --> `OkLCh`, `Lchuv` --> `LChuv`, `Lch99o` -->
   `LCh99o`, `LchChroma` --> `LChChroma`, `OklchChroma` --> `OkLChChroma`, and `Lchish` --> `LChish`.

 - **NEW**: Updated migration guide with recent plugin changes.
 - **NEW**: `coloraide.ColorAll` renamed and moved to `coloraide.everything.ColorAll`. This prevents unnecessary
   inclusion and allocation of objects that are not desired.
 - **NEW**: Default `Color` object now only registers `bradford` CAT by default, all others must be registered
   separately, or `coloraide.everything.Color` could be used.
 - **NEW**: All plugin classes must be instantiated when being registered. This allows some plugins to be instantiated
   with different defaults. This allows some plugins to be configured with different defaults.
diff --git a/docs/src/markdown/interpolation.md b/docs/src/markdown/interpolation.md
index 7dbc2931..ce48b93d 100644
--- a/docs/src/markdown/interpolation.md
+++ b/docs/src/markdown/interpolation.md
@@ -94,53 +94,54 @@ Color.interpolate(['orange', 'purple', 'green'], method='bezier')
 ## Hue Interpolation

 In interpolation, hues are handled special allowing us to control the way in which hues are evaluated. By default, the
 shortest angle between two hues is interpolated between, but the `hue` allows us to redefine this behavior in a number
 of different ways: `shorter`, `longer`, `increasing`, `decreasing`, and `specified`. Below, we can see how the
 interpolation varies using `shorter` vs `longer` (interpolate between the largest angle).

 ```playground
 i = Color.interpolate(
     ["lch(52% 58.1 22.7)", Color("lch(56% 49.1 257.1)").mask("hue", invert=True)],
     space="lch"
 )
 i(0.2477).to_string()
 i = Color.interpolate(
     ["lch(52% 58.1 22.7)", Color("lch(56% 49.1 257.1)").mask("hue", invert=True)],
     space="lch",
     hue="longer"
 )
 i(0.2477).to_string()
 ```

 To help visualize the different hue methods, consider the following evaluation between `#!color rebeccapurple` and
 `#!color lch(85% 85 805)`. Below we will demonstrate each of the different hue evaluations. To learn more, check out the
 [CSS level 4 specification](https://drafts.csswg.org/css-color-4/#hue-interpolation) which describes each one.

-!!! warning "Hue Fix-ups"
-    Hue "fix-ups" via the `hue` parameter currently only apply to linear interpolation, not Bezier. Bezier interpolation
-    will simply normalize the hues to be between 0˚ - 360˚.
+!!! tip "Interpolating Multiple Colors"
+    The algorithm has been tweaked in order to calculate fix-ups of multiple hues such that they are all relative to
+    each other. This is a requirement for interpolation methods such as Bezier that evaluate all hues at the same time
+    as opposed to the linear, piecewise interpolation that only evaluates two hues at any given time.

 === "shorter"
     ```playground
     Color.interpolate(
         ["rebeccapurple", "lch(85% 100 805)"],
         space='lch',
         hue="shorter"
     )
     ```

 === "longer"
     ```playground
     Color.interpolate(
         ["rebeccapurple", "lch(85% 100 805)"],
         space='lch',
         hue="longer"
     )
     ```

 === "increasing"
     ```playground
     Color.interpolate(
         ["rebeccapurple", "lch(85% 100 805)"],
         space='lch',
         hue="increasing"
diff --git a/docs/src/markdown/plugins/index.md b/docs/src/markdown/plugins/index.md
index 6bdb625e..8723e1ce 100644
--- a/docs/src/markdown/plugins/index.md
+++ b/docs/src/markdown/plugins/index.md
@@ -1,11 +1,12 @@
 # ColorAide Plugins

 ColorAide implements extendable portions of the `Color` object as plugins. This makes adding things such as new ∆E
 methods or even new color spaces quite easy. Currently, ColorAide implements the following areas as plugins:

 - [∆E methods](./delta_e.md)
 - [Fit/Gamut mapping](./fit.md)
 - [Chromatic adaptation](./cat.md)
 - [Filters](./filter.md)
 - [Contrast](./contrast.md)
 - [Color spaces](./space.md)
+- [Interpolation](./interpolate.md)
diff --git a/docs/src/markdown/plugins/interpolate.md b/docs/src/markdown/plugins/interpolate.md
new file mode 100644
index 00000000..7d54e27d
--- /dev/null
+++ b/docs/src/markdown/plugins/interpolate.md
@@ -0,0 +1,55 @@
+# Interpolation
+
+## Description
+
+Interpolation plugins allow for interpolation between one or more colors. All interpolation in ColorAide is provided via
+plugins.
+
+## Plugin Class
+
+Plugins are are created by subclassing `#!py3 coloraide.interpolate.Interpolate`.
+
+```py
+class Interpolate(Plugin, metaclass=ABCMeta):
+    """Interpolation plugin."""
+
+    NAME = ""
+
+    @abstractmethod
+    def get_interpolator(self) -> Type[Interpolator]:
+        """Get the interpolator object."""
+```
+
+Once registered, the plugin can then be used via `interpolate`, `steps`, or `mix` by passing its `NAME` via the `method`
+parameter along with any additional key word arguments to override default behavior. An `Interpolator` object will be
+returned which allows for interpolating between the given list of `colors`.
+
+```py
+color.interpolate(colors, method=NAME)
+```
+
+In general, the `Interpolate` plugin is mainly a wrapper to ensure the interpolation setup uses an appropriate
+`Interpolator` object which does the actual work. An interpolation plugin should derive their `Interpolate` class from
+`coloraide.interpolate.Interpolator`. While we won't show all the methods of the class, we will show the one function
+that must be defined.
+
+```py
+class Interpolator(metaclass=ABCMeta):
+    """Interpolator."""
+
+    @abstractmethod
+    def interpolate(
+        self,
+        easing: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+        point: float,
+        index: int,
+    ) -> Vector:
+        """Interpolate."""
+```
+
+`Interpolator.interpolate` expects an optional `easing` method that can be either a callback, or a dictionary of
+easing functions for specific color channels. Additionally, it accepts a user defined `point` indicating where between
+the colors the user is requesting the new color and the `index`, within the list of colors to be interpolated,
+of the color to the right of the `point`. The function should return the interpolated coordinates for the color.
+
+Check out the source code to see some example plugins.
diff --git a/docs/src/mkdocs.yml b/docs/src/mkdocs.yml
index fb7de009..6dc12889 100644
--- a/docs/src/mkdocs.yml
+++ b/docs/src/mkdocs.yml
@@ -93,50 +93,51 @@ nav:
         - LCh(uv): colors/lchuv.md
         - DIN99 LCh: colors/lch99o.md
         - JzCzhz: colors/jzczhz.md

       - ACES Spaces:
         - ACES 2065-1: colors/aces2065_1.md
         - ACEScg: colors/acescg.md
         - ACEScc: colors/acescc.md
         - ACEScct: colors/acescct.md

       - Miscellaneous Spaces:
         - xyY: colors/xyy.md
         - CMY: colors/cmy.md
         - CMYK: colors/cmyk.md
         - oRGB: colors/orgb.md
         - Prismatic: colors/prismatic.md

   - Plugins:
       - plugins/index.md
       - Delta E: plugins/delta_e.md
       - Fit/Gamut Mapping: plugins/fit.md
       - Chromatic Adaptation: plugins/cat.md
       - Filters: plugins/filter.md
       - Contrast: plugins/contrast.md
       - Color Spaces: plugins/space.md
+      - Interpolation: plugins/interpolate.md

   - API:
       - Color API: api/index.md
   - Playground: playground.md
   - About:
       - Contributing &amp; Support: about/contributing.md
       - Acknowledgments: about/acknowledgments.md
       - Changelog: about/changelog.md
       - Migration Notes:
           - '1.0': about/releases/1.0.md
       - License: about/license.md

 markdown_extensions:
   - markdown.extensions.toc:
       slugify: !!python/object/apply:pymdownx.slugs.slugify {kwds: {case: lower}}
       permalink: ""
   - markdown.extensions.admonition:
   - markdown.extensions.smarty:
       smart_quotes: false
   - pymdownx.betterem:
   - markdown.extensions.attr_list:
   - markdown.extensions.def_list:
   - markdown.extensions.tables:
   - markdown.extensions.abbr:
   - markdown.extensions.footnotes:
diff --git a/mkdocs.yml b/mkdocs.yml
index 5ecaaed1..b4c15537 100644
--- a/mkdocs.yml
+++ b/mkdocs.yml
@@ -93,50 +93,51 @@ nav:
         - LCh(uv): colors/lchuv.md
         - DIN99 LCh: colors/lch99o.md
         - JzCzhz: colors/jzczhz.md

       - ACES Spaces:
         - ACES 2065-1: colors/aces2065_1.md
         - ACEScg: colors/acescg.md
         - ACEScc: colors/acescc.md
         - ACEScct: colors/acescct.md

       - Miscellaneous Spaces:
         - xyY: colors/xyy.md
         - CMY: colors/cmy.md
         - CMYK: colors/cmyk.md
         - oRGB: colors/orgb.md
         - Prismatic: colors/prismatic.md

   - Plugins:
       - plugins/index.md
       - Delta E: plugins/delta_e.md
       - Fit/Gamut Mapping: plugins/fit.md
       - Chromatic Adaptation: plugins/cat.md
       - Filters: plugins/filter.md
       - Contrast: plugins/contrast.md
       - Color Spaces: plugins/space.md
+      - Interpolation: plugins/interpolate.md

   - API:
       - Color API: api/index.md
   - Playground: playground.md
   - About:
       - Contributing &amp; Support: about/contributing.md
       - Acknowledgments: about/acknowledgments.md
       - Changelog: about/changelog.md
       - Migration Notes:
           - '1.0': about/releases/1.0.md
       - License: about/license.md

 markdown_extensions:
   - markdown.extensions.toc:
       slugify: !!python/object/apply:pymdownx.slugs.slugify {kwds: {case: lower}}
       permalink: ""
   - markdown.extensions.admonition:
   - markdown.extensions.smarty:
       smart_quotes: false
   - pymdownx.betterem:
   - markdown.extensions.attr_list:
   - markdown.extensions.def_list:
   - markdown.extensions.tables:
   - markdown.extensions.abbr:
   - markdown.extensions.footnotes:
diff --git a/tests/test_interpolation.py b/tests/test_interpolation.py
index 8bbe12bb..197cba31 100644
--- a/tests/test_interpolation.py
+++ b/tests/test_interpolation.py
@@ -194,100 +194,100 @@ class TestInterpolation(util.ColorAsserts, unittest.TestCase):
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.25, hue="longer", space="lch"),
             Color("rgb(-86.817 87.629 170)")
         )
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.25, hue="increasing", space="lch"),
             Color("rgb(146.72 -3.9233 106.41)")
         )
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.25, hue="decreasing", space="lch"),
             Color("rgb(-86.817 87.629 170)")
         )
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.25, hue="specified", space="lch"),
             Color("rgb(112.83 63.969 -28.821)")
         )

     def test_hue_shorter_cases(self):
         """Cover shorter hue cases."""

         # c2 - c1 > 180
         c1 = Color('lch(75% 50 40)')
         c2 = Color('lch(30% 30 350)')
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch"),
-            Color("lch(75% 50 375)")
+            Color("lch(75% 50 15)")
         )

         # c2 - c1 < -180
         c1 = Color('lch(30% 30 350)')
         c2 = Color('lch(75% 50 40)')
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch"),
             Color("lch(30% 30 375)")
         )

     def test_hue_longer_cases(self):
         """Cover longer hue cases."""

         # 0 < (c2 - c1) < 180
         c1 = Color('lch(75% 50 40)')
         c2 = Color('lch(30% 30 60)')
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.50, hue="longer", space="lch"),
-            Color("lch(75% 50 230)")
+            Color("lch(75% 50 -130)")
         )

         # -180 < (c2 - c1) < 0
         c1 = Color('lch(30% 30 60)')
         c2 = Color('lch(75% 50 40)')
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.50, hue="longer", space="lch"),
             Color("lch(30% 30 230)")
         )

     def test_hue_increasing_cases(self):
         """Cover increasing hue cases."""

         # c2 < c1
         c1 = Color('lch(75% 50 60)')
         c2 = Color('lch(30% 30 40)')
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.50, hue="increasing", space="lch"),
             Color("lch(75% 50 230)")
         )

     def test_hue_decreasing_cases(self):
         """Cover decreasing hue cases."""

         # c1 < c2
         c1 = Color('lch(75% 50 40)')
         c2 = Color('lch(30% 30 60)')
         self.assertColorEqual(
             c1.mix(c2.mask("hue", invert=True), 0.50, hue="decreasing", space="lch"),
-            Color("lch(75% 50 230)")
+            Color("lch(75% 50 -130)")
         )

     def test_mix_hue_adjust_bad(self):
         """Test hue adjusting."""

         c1 = Color('rebeccapurple')
         c2 = Color('lch(85% 100 805)')
         with self.assertRaises(ValueError):
             c1.mix(c2.mask("hue", invert=True), 0.25, hue="bad", space="lch")

     def test_mix_hue_nan(self):
         """Test mix hue with `NaN`."""

         self.assertColorEqual(
             Color('hsl', [NaN, 0, 0.25]).mix(Color('hsl', [NaN, 0, 0.9]), 0.50, space="hsl"),
             Color("hsl(0, 0%, 57.5%)")
         )

         self.assertColorEqual(
             Color('hsl', [NaN, 0, 0.25]).mix(Color('hsl', [120, 0.5, 0.9]), 0.50, space="hsl"),
             Color("hsl(120, 25%, 57.5%)")
         )

         self.assertColorEqual(
             Color('hsl', [120, 0.5, 0.25]).mix(Color('hsl', [NaN, 0, 0.9]), 0.50, space="hsl"),
@@ -951,27 +951,27 @@ class TestInterpolation(util.ColorAsserts, unittest.TestCase):
             )(0.5),
             Color('rgb(89.25 204 178.5)')
         )

     def test_bezier_cylindrical(self):
         """Test bezier with a cylindrical space."""

         self.assertColorEqual(
             Color.interpolate(
                 ['hsl(250 50% 30%)', 'hsl(none 0% 10%)', 'hsl(120 75% 75%)'],
                 space='hsl',
                 method='bezier'
             )(0.75),
             Color('hsl(176.88 45.313% 47.813%)')
         )

     def test_bezier_cylindrical_gamut(self):
         """Test bezier with a cylindrical space with at least one color out of gamut."""

         self.assertColorEqual(
             Color.interpolate(
                 ['hsl(250 50% 30%)', 'hsl(none 0% 110%)'],
                 space='hsl',
                 method='bezier'
             )(0.75),
-            Color('hsl(62.5 12.5% 82.5%)')
+            Color('hsl(332.5 12.5% 82.5%)')
         )
diff --git a/tests/test_plugin.py b/tests/test_plugin.py
index fe18778c..b36abbf7 100644
--- a/tests/test_plugin.py
+++ b/tests/test_plugin.py
@@ -227,50 +227,72 @@ class TestCustom(util.ColorAsserts, unittest.TestCase):
                 method='von-kries'
             )

     def test_plugin_registration_filter(self):
         """Test plugin registration of `filter`."""

         from coloraide.filters.w3c_filter_effects import Sepia

         # Deregistration should have taken place
         class Custom(Color):
             pass

         Custom.deregister('filter:sepia')
         color = Color('red').filter('sepia')

         with self.assertRaises(ValueError):
             Custom('red').filter('sepia')

         # Now it is registered again
         Custom.register(Sepia())
         self.assertColorEqual(
             Custom('red').filter('sepia'),
             color
         )

+    def test_plugin_registration_interpolate(self):
+        """Test plugin registration of `interpolate`."""
+
+        from coloraide.interpolate.bezier import InterpolateBezier
+
+        # Deregistration should have taken place
+        class Custom(Color):
+            pass
+
+        Custom.deregister('interpolate:bezier')
+        color = Color('red').mix('blue', method='bezier')
+
+        with self.assertRaises(ValueError):
+            Custom('red').mix('blue', method='bezier')
+
+        # Now it is registered again
+        Custom.register(InterpolateBezier())
+        self.assertColorEqual(
+            Custom('red').mix('blue', method='bezier'),
+            color
+        )
+
     def test_deregister_all_category(self):
         """Test deregistration of all plugins in a category."""

         class Custom(Color):
             pass

         Custom.deregister('fit:*')
         self.assertEqual(Custom.FIT_MAP, {})
         self.assertNotEqual(Custom.CS_MAP, {})

     def test_deregister_all(self):
         """Test deregistration of all plugins."""

         class Custom(Color):
             pass

         Custom.deregister('*')
         self.assertEqual(Custom.FIT_MAP, {})
         self.assertEqual(Custom.CS_MAP, {})
         self.assertEqual(Custom.DE_MAP, {})

     def test_reserved_registration_fit(self):
         """Test override registration of reserved fit method."""

         from coloraide.gamut import Fit
diff --git a/tests/test_stops.py b/tests/test_stops.py
index e02da807..3ac674dc 100644
--- a/tests/test_stops.py
+++ b/tests/test_stops.py
@@ -1,26 +1,26 @@
 """Test API."""
-from coloraide.interpolate.common import calc_stops
+from coloraide.interpolate import calc_stops
 import pytest


 class TestStops:
     """
     Test stops.

     https://drafts.csswg.org/css-images-4/#color-stop-fixup
     """

     STOPS = [
         ({1: 0.2}, 3, {0: 0, 1: 0.2, 2: 1.0}),
         ({0: 0.4}, 4, {0: 0.4, 1: 0.6, 2: 0.8, 3: 1.0}),
         ({0: -0.5}, 3, {0: -0.5, 1: 0.25, 2: 1.0}),
         ({0: 0.2, 1: 0.0, 2: 0.4}, 3, {0: 0.2, 1: 0.2, 2: 0.4}),
         ({1: -0.5, 2: 1.5}, 4, {0: 0.0, 1: 0.0, 2: 1.5, 3: 1.5}),
         ({0: 0.8, 1: 0.0, 3: 1.0}, 4, {0: 0.8, 1: 0.8, 2: 0.9, 3: 1.0})
     ]

     @pytest.mark.parametrize('stops1,count,stops2', STOPS)
     def test_stops(self, stops1, count, stops2):
         """Test stops."""

         assert calc_stops(stops1, count) == stops2
