#!/usr/bin/env python3
import argparse
import shutil
import subprocess
import sys
from pathlib import Path

from git import Repo
from git.exc import GitError


class RepoManager:
    """Handles repository caching and retrieval."""

    def __init__(self, cache_dir: Path | None = None):
        self.cache_dir = cache_dir or Path.cwd() / ".cached_repos"
        self.cache_dir.mkdir(exist_ok=True)

    def get_cache_path(self, repo_path: str, commit_hash: str) -> Path:
        """Get the cache path for a repo+commit combination."""
        safe_repo_name = repo_path.replace("/", "__")
        return self.cache_dir / f"{safe_repo_name}__{commit_hash}"

    def get_cached_repo(self, repo_path: str, commit_hash: str) -> Path:
        """Get cached repository, downloading if needed. Returns path to cached repo."""
        cache_path = self.get_cache_path(repo_path, commit_hash)

        if cache_path.exists():
            print(f"✓ Using cached repository: {cache_path.name}")
            return cache_path

        return self._download_and_cache_repo(repo_path, commit_hash, cache_path)

    def _download_and_cache_repo(
        self, repo_path: str, commit_hash: str, cache_path: Path
    ) -> Path:
        """Download and cache a repository at the specified commit."""
        print(f"📥 Caching repository: {repo_path} @ {commit_hash}")

        repo_url = f"https://github.com/{repo_path}.git"

        # Always use git fetch approach for reliability
        try:
            # Clone without checking out files
            repo = Repo.clone_from(repo_url, cache_path, no_checkout=True)

            # Fetch the specific commit
            repo.git.fetch("origin", commit_hash)

            # Checkout the commit
            repo.git.checkout(commit_hash)

            print(f"✅ Cached via git fetch: {cache_path.name}")
            return cache_path

        except GitError as e:
            error_str = str(e)
            if "No route to host" in error_str or "ssh: connect to host" in error_str:
                print(f"❌ Git operation failed for {repo_path}: {e}")
                print(
                    "    This is likely a network connectivity issue. Please try running again."
                )
            elif (
                "not found" in error_str.lower()
                or "does not exist" in error_str.lower()
            ):
                print(f"❌ Commit {commit_hash} not found in {repo_path}")
                print(
                    "    This commit may have been deleted or is not publicly available."
                )
            else:
                print(f"❌ Git operation failed for {repo_path}: {e}")
            sys.exit(1)


class GitDiffComparator:
    """Handles git operations and diff comparisons."""

    DIFF_CONFIGS = [
        {"file": "1.diff", "folder": "diff_1"},
        {"file": "2.diff", "folder": "diff_2"},
        {"file": "ground_truth.diff", "folder": "ground_truth"},
    ]

    # Common binary file patterns to exclude when applying diffs
    BINARY_PATTERNS = [
        "*.db",
        "*.ico",
        "*.png",
        "*.jpg",
        "*.jpeg",
        "*.gif",
        "*.pdf",
        "*.zip",
        "*.tar",
        "*.gz",
        "*.exe",
        "*.dll",
        "*.so",
        "*.dylib",
        "*.pbz",
        "*.pbz2",
        "*.pkl",
        "*.pickle",
        "*.sqlite",
        "*.sqlite3",
        "*.bin",
        "*.dat",
        "*.img",
        "*.iso",
        "*.dmg",
        "*.app",
        "*.pyc",
    ]

    def __init__(
        self, repo_path: str, commit_before: str, work_dir: Path | None = None
    ):
        self.repo_path = repo_path
        self.commit_before = commit_before
        self.repo_name = repo_path.split("/")[-1]
        self.project_folder = f"{self.repo_name}__comparison"
        self.work_dir = work_dir or Path.cwd()
        self.project_path = self.work_dir / self.project_folder
        self.repo_manager = RepoManager()

    def setup_project_folder(self) -> None:
        """Create the main project folder if it doesn't exist."""
        if self.project_path.exists():
            print(f"❌ Project folder '{self.project_folder}' already exists.")
            print("Please remove it or choose a different repo before running again.")
            sys.exit(1)

        self.project_path.mkdir()
        print(f"Created project folder: {self.project_folder}")

    def copy_folder(self, src_path: Path, dest_path: Path) -> None:
        """Copy a folder, removing destination if it exists."""
        if dest_path.exists():
            shutil.rmtree(dest_path)

        # Copy the working tree while preserving symlinks and ignoring dangling ones
        shutil.copytree(
            src_path,
            dest_path,
            symlinks=True,
            ignore_dangling_symlinks=True,
        )

    def check_for_binary_files(self, diff_content: str) -> list[str]:
        """Check if diff contains binary files that would be excluded."""
        import fnmatch

        binary_files_found = []

        for line in diff_content.split("\n"):
            if not line.startswith("diff --git"):
                continue

            parts = line.split()
            if len(parts) < 4:
                continue

            # Extract file path (prefer a/ path, fallback to raw)
            file_path = parts[2][2:] if parts[2].startswith("a/") else parts[2]

            # Check if file matches any binary pattern
            if any(
                fnmatch.fnmatch(file_path, pattern) for pattern in self.BINARY_PATTERNS
            ):
                if file_path not in binary_files_found:
                    binary_files_found.append(file_path)

        return binary_files_found

    def extract_error_message(self, stderr_output: str, returncode: int) -> str:
        """Extract meaningful error message from git output."""
        lines = stderr_output.strip().split("\n")

        # First priority: find lines starting with "error:" or "fatal:"
        for line in lines:
            line = line.strip()
            if line.startswith(("error:", "fatal:")):
                return line

        # Second priority: find other meaningful error lines (but not "Checking patch")
        for line in lines:
            line = line.strip()
            if (
                line
                and not line.startswith("Checking patch")
                and any(
                    keyword in line.lower()
                    for keyword in ["failed", "conflict", "corrupt", "cannot", "unable"]
                )
            ):
                return line

        # Fallback: return a generic message
        return f"Patch application failed (exit code {returncode})"

    def apply_diff(self, repo_path: Path, diff_file: str) -> tuple[bool, str]:
        """Apply a diff file to the repository with detailed error reporting.
        
        Returns:
            tuple: (success: bool, description: str)
        """
        diff_path = self.work_dir / diff_file

        # Check if diff file exists and is readable
        if not diff_path.exists():
            return False, f"Diff file '{diff_file}' not found"

        if diff_path.stat().st_size == 0:
            return False, f"Empty diff file: {diff_file}"

        try:
            # Validate diff file content and check for binary files
            content = diff_path.read_text(encoding="utf-8", errors="ignore")
            if not content.strip():
                return False, f"Empty diff file: {diff_file}"

            # Normalize line endings to LF
            content = content.replace("\r\n", "\n").replace("\r", "\n")

            # Ensure diff ends with newline (required for proper git apply)
            if content and not content.endswith("\n"):
                content += "\n"

            # Write back normalized content
            diff_path.write_text(content, encoding="utf-8")

            # Check for binary files in the diff
            binary_files = self.check_for_binary_files(content)

        except Exception as e:
            return False, f"Cannot read diff file '{diff_file}': {e}"

        try:
            # Build exclude arguments for binary files
            exclude_args = []
            for pattern in self.BINARY_PATTERNS:
                exclude_args.extend(["--exclude", pattern])

            # Apply the diff
            apply_cmd = (
                ["git", "apply", "--ignore-whitespace"]
                + exclude_args
                + [str(diff_path.absolute())]
            )
            result = subprocess.run(
                apply_cmd,
                cwd=repo_path,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                if binary_files:
                    binary_list = ", ".join(binary_files)
                    return True, f"Applied successfully (excluded {len(binary_files)} binary file(s) from {diff_file}: {binary_list})"
                else:
                    return True, "Applied successfully"
            else:
                error_msg = self.extract_error_message(result.stderr, result.returncode)
                return False, f"Failed to apply: {error_msg}"

        except FileNotFoundError:
            return False, "Git not found - please install Git"
        except Exception as e:
            return False, f"Unexpected error: {e}"

    def print_results_summary(self, diff_results: list[tuple[str, bool, str]]) -> None:
        """Print a clean summary of diff application results."""
        print("\n" + "=" * 60)
        print("GIT APPLY RESULTS")
        print("=" * 60)

        total_binary_exclusions = 0
        binary_files_by_diff = []
        
        for i, (diff_file, success, description) in enumerate(diff_results):
            folder_name = self.DIFF_CONFIGS[i]["folder"]
            status = "✅" if success else "❌"
            print(f"{folder_name}: {status} {description}")

            # Extract binary file details from description
            if success and "excluded" in description and "binary file" in description:
                import re
                # Look for pattern like "excluded N binary file(s) from diff_file: file1, file2, ..."
                match = re.search(r"excluded (\d+) binary file\(s\) from ([^:]+): (.+)", description)
                if match:
                    count = int(match.group(1))
                    source_diff = match.group(2)
                    files_list = match.group(3)
                    total_binary_exclusions += count
                    binary_files_by_diff.append((source_diff, files_list, count))

        success_count = sum(1 for _, success, _ in diff_results if success)
        total_count = len(diff_results)
        print("=" * 60)
        print(f"{success_count}/{total_count} diffs applied successfully")

        if total_binary_exclusions > 0:
            print(f"⚠️  {total_binary_exclusions} binary files were excluded from diffs")
            for diff_file, files_list, count in binary_files_by_diff:
                print(f"   • {diff_file}: {files_list}")
            print(
                "   (This indicates the model(s) likely included files in their commit that they shouldn't have)"
            )

    def run(self) -> None:
        """Execute the complete diff comparison process."""
        self.setup_project_folder()

        all_folders = ["before"] + [config["folder"] for config in self.DIFF_CONFIGS]
        diff_results = []
        any_failures = False

        try:
            # Set up before folder - always use RepoManager with caching
            before_path = self.project_path / "before"

            # Get cached repository (downloads and caches if not already cached)
            cached_repo_path = self.repo_manager.get_cached_repo(
                self.repo_path, self.commit_before
            )
            print("Setting up 'before' folder from cache...")
            self.copy_folder(cached_repo_path, before_path)

            # Create diff folders
            print("Creating diff folders...")
            for config in self.DIFF_CONFIGS:
                dest_path = self.project_path / config["folder"]
                self.copy_folder(before_path, dest_path)

            # Apply diffs - continue even if some fail
            print("Applying diffs...")
            for config in self.DIFF_CONFIGS:
                folder_path = self.project_path / config["folder"]
                success, description = self.apply_diff(folder_path, config["file"])
                diff_results.append((config["file"], success, description))
                if not success:
                    any_failures = True

            # Print project structure
            print(f"\nProject folder: {self.project_folder}/")
            for folder in all_folders:
                print(f"  ├── {folder}/")

            # Print results summary
            self.print_results_summary(diff_results)
            
            # Exit with code 1 if any diffs failed
            if any_failures:
                sys.exit(1)

        except GitError as e:
            print(f"❌ Git error: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            print("\n❌ Operation cancelled by user")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Clone repo and apply diffs to multiple copies with detailed error reporting"
    )
    parser.add_argument("repo_path", help="Git repository path (e.g., owner/repo)")
    parser.add_argument(
        "commit_before", help="Commit hash to reset to (before changes)"
    )
    parser.add_argument(
        "--work-dir",
        type=Path,
        help="Directory to look for diff files and create output (defaults to current directory)",
    )

    args = parser.parse_args()

    comparator = GitDiffComparator(args.repo_path, args.commit_before, args.work_dir)
    comparator.run()


if __name__ == "__main__":
    main()
