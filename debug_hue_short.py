#!/usr/bin/env python3

from coloraide import Color

# Test the first failing case
c1 = Color('lch(75% 50 40)')
c2 = Color('lch(30% 30 350)')

print(f"c1: {c1}")
print(f"c2: {c2}")
print(f"c2.mask('hue', invert=True): {c2.mask('hue', invert=True)}")

result = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
print(f"Result: {result}")
print(f"Expected: lch(75% 50 15)")

# Let's also test without masking
result_no_mask = c1.mix(c2, 0.50, hue="shorter", space="lch")
print(f"Without mask: {result_no_mask}")
