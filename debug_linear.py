#!/usr/bin/env python3

from coloraide import Color
from coloraide.interpolate import color_piecewise_lerp

# Test the specific case with linear interpolation
colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
linear_interp = color_piecewise_lerp(
    Color, colors, 'hsl', 'hsl',
    progress=None, hue='shorter', premultiplied=True
)
print(f"Linear color_map: {linear_interp.color_map}")

result = linear_interp(0.75)
print(f"Linear result: {result}")

# Let's also test intermediate values
for t in [0.0, 0.25, 0.5, 0.75, 1.0]:
    result = linear_interp(t)
    print(f"t={t}: {result}")
