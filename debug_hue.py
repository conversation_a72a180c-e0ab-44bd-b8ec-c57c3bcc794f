#!/usr/bin/env python3

from coloraide import Color

# Test the specific case
colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
result = Color.interpolate(colors, space='hsl', method='bezier')(0.75)
print(f"Result: {result}")
print(f"Expected: hsl(332.5 12.5% 82.5%)")

# Let's also test with linear interpolation
result_linear = Color.interpolate(colors, space='hsl', method='linear')(0.75)
print(f"Linear result: {result_linear}")

# Check the colors individually
c1 = Color('hsl(250 50% 30%)')
c2 = Color('hsl(none 0% 110%)')
print(f"c1: {c1}")
print(f"c2: {c2}")
print(f"c1 hue: {c1['hue']}")
print(f"c2 hue: {c2['hue']}")
