#!/usr/bin/env python3

from coloraide import Color
from coloraide.interpolate.piecewise import adjust_hues

# Test case 1
print("=== Case 1 ===")
c1 = Color('lch(75% 50 40)')
c2 = Color('lch(30% 30 350)').mask("hue", invert=True)
print(f"Before adjust_hues: c1={c1['hue']}, c2={c2['hue']}")

# Apply the adjust logic manually (simulating what happens internally)
c2_full = Color('lch(30% 30 350)')  # Original unmasked color for hue adjustment
c1_copy = c1.clone()
c2_copy = c2_full.clone()
adjust_hues(c1_copy, c2_copy, "shorter")
print(f"After adjust_hues: c1={c1_copy['hue']}, c2={c2_copy['hue']}")
print(f"Mid-point: {(c1_copy['hue'] + c2_copy['hue']) / 2}")
print()

# Test case 2
print("=== Case 2 ===")
c1 = Color('lch(30% 30 350)')
c2 = Color('lch(75% 50 40)').mask("hue", invert=True)
print(f"Before adjust_hues: c1={c1['hue']}, c2={c2['hue']}")

c2_full = Color('lch(75% 50 40)')  # Original unmasked color for hue adjustment
c1_copy = c1.clone()
c2_copy = c2_full.clone()
adjust_hues(c1_copy, c2_copy, "shorter")
print(f"After adjust_hues: c1={c1_copy['hue']}, c2={c2_copy['hue']}")
print(f"Mid-point: {(c1_copy['hue'] + c2_copy['hue']) / 2}")
