"""
Interpolation methods.

Originally, the base code for `interpolate`, `mix` and `steps` was ported from the
https://colorjs.io project. Since that time, there has been significant modifications
that add additional features etc. The base logic though is attributed to the original
authors.

In general, the logic mimics in many ways the `color-mix` function as outlined in the Level 5
color draft (Oct 2020), but the initial approach was modeled directly off of the work done in
color.js.
---
Original Authors: <AUTHORS>
"""
from .bezier import color_bezier_lerp
from .piecewise import color_piecewise_lerp
from .common import Interpolator, hint, stop, calc_stops  # noqa: F401
from typing import Callable, Dict, TYPE_CHECKING

if TYPE_CHECKING:  # pragma: no cover
    from ..color import Color

__all__ = ('stop', 'hint', 'get_interpolator', 'calc_stops')


SUPPORTED = {
    "linear": color_piecewise_lerp,
    "bezier": color_bezier_lerp
}  # type: Dict[str, Callable[..., Interpolator]]


def get_interpolator(interpolator: str, color_class: 'Color' = None) -> Callable[..., Interpolator]:
    """Get desired interpolation method."""

    # Use plugin system if color_class is available
    if color_class is not None and hasattr(color_class, 'INTERPOLATE_MAP'):
        plugin = color_class.INTERPOLATE_MAP.get(interpolator)
        if plugin is not None:
            return plugin
        # If plugin system is available but plugin not found, raise error immediately
        raise ValueError("'{}' is not a recognized interpolator".format(interpolator))

    # Fallback to hardcoded SUPPORTED dict (for backward compatibility)
    try:
        return SUPPORTED[interpolator]
    except KeyError:
        raise ValueError("'{}' is not a recognized interpolator".format(interpolator))
