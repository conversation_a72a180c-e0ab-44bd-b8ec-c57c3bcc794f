#!/usr/bin/env python3

from coloraide import Color
from coloraide.interpolate import color_bezier_lerp

# Test the specific case
c1 = Color('hsl(250 50% 30%)')
c2 = Color('hsl(none 0% 110%)')

print(f"c1: {c1}")
print(f"c2: {c2}")
print(f"c1 coords: {c1[:]}")
print(f"c2 coords: {c2[:]}")

# Test with manual bezier call
colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
bezier_interp = color_bezier_lerp(
    Color, colors, 'hsl', 'hsl',
    progress=None, hue='shorter', premultiplied=True
)
print(f"Bezier coords: {bezier_interp.coordinates}")

result = bezier_interp(0.75)
print(f"Result: {result}")
print(f"Result coords: {result[:]}")
