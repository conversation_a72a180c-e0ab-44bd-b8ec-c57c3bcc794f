diff --git a/coloraide/interpolate/__init__.py b/coloraide/interpolate/__init__.py
index 0d9c383..273f704 100644
--- a/coloraide/interpolate/__init__.py
+++ b/coloraide/interpolate/__init__.py
@@ -1,36 +1,36 @@
 """
 Interpolation methods.

 Originally, the base code for `interpolate`, `mix` and `steps` was ported from the
 https://colorjs.io project. Since that time, there has been significant modifications
 that add additional features etc. The base logic though is attributed to the original
 authors.

 In general, the logic mimics in many ways the `color-mix` function as outlined in the Level 5
 color draft (Oct 2020), but the initial approach was modeled directly off of the work done in
 color.js.
 ---
 Original Authors: <AUTHORS>
 License: MIT (As noted in https://github.com/LeaVerou/color.js/blob/master/package.json)
 """
 from .bezier import color_bezier_lerp
 from .piecewise import color_piecewise_lerp
-from .common import Interpolator, hint, stop  # noqa: F401
+from .common import Interpolator, hint, stop, calc_stops  # noqa: F401
 from typing import Callable, Dict

-__all__ = ('stop', 'hint', 'get_interpolator')
+__all__ = ('stop', 'hint', 'get_interpolator', 'calc_stops')


 SUPPORTED = {
     "linear": color_piecewise_lerp,
     "bezier": color_bezier_lerp
 }  # type: Dict[str, Callable[..., Interpolator]]


 def get_interpolator(interpolator: str) -> Callable[..., Interpolator]:
     """Get desired blend mode."""

     try:
         return SUPPORTED[interpolator]
     except KeyError:
         raise ValueError("'{}' is not a recognized interpolator".format(interpolator))
diff --git a/coloraide/interpolate/bezier.py b/coloraide/interpolate/bezier.py
index a0b4e90..e3d757a 100644
--- a/coloraide/interpolate/bezier.py
+++ b/coloraide/interpolate/bezier.py
@@ -1,36 +1,95 @@
 """Bezier interpolation."""
 from .. import algebra as alg
 from ..spaces import Cylindrical
 from ..types import Vector, ColorInput
 from typing import Optional, Callable, Sequence, Mapping, Type, Dict, List, Union, cast, Any, TYPE_CHECKING
 from .common import stop, Interpolator, calc_stops, process_mapping, premultiply, postdivide

 if TYPE_CHECKING:  # pragma: no cover
     from ..color import Color


+def adjust_bezier_hues(colors: List['Color'], hue: str) -> None:
+    """Adjust hues for bezier interpolation."""
+
+    if hue == "specified":
+        return
+
+    # For bezier, we need to handle all colors together
+    # First, find all the defined hues and normalize NaN % 360 → 0
+    hue_name = None
+    for color in colors:
+        if isinstance(color._space, Cylindrical):
+            hue_name = cast(Cylindrical, color._space).hue_name()
+            break
+
+    if hue_name is None:
+        return
+
+    # Get all hue values and normalize NaN to 0
+    hues = []
+    for color in colors:
+        h = color.get(hue_name)
+        h = h % 360 if not alg.is_nan(h) else 0.0
+        hues.append(h)
+
+    # Apply hue adjustments between consecutive pairs
+    for i in range(1, len(hues)):
+        c1, c2 = hues[i-1], hues[i]
+
+        if hue == "shorter":
+            if c2 - c1 > 180:
+                c1 += 360
+                hues[i-1] = c1
+            elif c2 - c1 < -180:
+                c2 += 360
+                hues[i] = c2
+
+        elif hue == "longer":
+            if 0 < (c2 - c1) < 180:
+                c1 += 360
+                hues[i-1] = c1
+            elif -180 < (c2 - c1) <= 0:
+                c2 += 360
+                hues[i] = c2
+
+        elif hue == "increasing":
+            if c2 < c1:
+                c2 += 360
+                hues[i] = c2
+
+        elif hue == "decreasing":
+            if c1 < c2:
+                c1 += 360
+                hues[i-1] = c1
+
+    # Set the adjusted hues back
+    for color, h in zip(colors, hues):
+        color.set(hue_name, h)
+
+
 def binomial_row(n: int) -> List[int]:
     """
     Binomial row.

     Return row in Pascal's triangle.
     """

     row = [1, 1]
     for i in range(n - 1):
         r = [1]
         x = 0
         for x in range(1, len(row)):
             r.append(row[x] + row[x - 1])
         r.append(row[x])
         row = r
     return row


 class InterpolateBezier(Interpolator):
     """Interpolate Bezier."""

     def __init__(
         self,
         coordinates: List[Vector],
         names: Sequence[str],
@@ -123,126 +182,136 @@ class InterpolateBezier(Interpolator):
         """Interpolate."""

         percent = alg.clamp(p, 0.0, 1.0)
         if percent > self.end:
             percent = self.end
         elif percent < self.start:
             percent = self.start
         last = self.start
         for i in range(1, self.length):
             s = self.stops[i]
             if percent <= s:
                 r = s - last
                 p2 = (percent - last) / r if r else 1
                 easing = self.easings[i - 1]  # type: Any
                 if easing is None:
                     easing = self.progress
                 piece = 1 / (self.length - 1)
                 return self.interpolate(easing, p2, (i - 1) * piece, i * piece)
             last = s

         # We shouldn't ever hit this, but provided for typing.
         # If we do hit this, it would be a bug.
         raise RuntimeError('Iterpolation could not be found for {}'.format(percent))  # pragma: no cover


-def normalize_color(color: 'Color', space: str, premultiplied: bool) -> None:
+def normalize_color(color: 'Color', space: str, premultiplied: bool, hue: str = "shorter") -> None:
     """Normalize color."""

+    # For 'specified' hue mode, preserve NaN hues and avoid fitting that might normalize them
+    preserve_nan_hue = (hue == "specified" and
+                       isinstance(color._space, Cylindrical) and
+                       alg.is_nan(color.get(cast(Cylindrical, color._space).hue_name())))
+
     # Adjust to color to space and ensure it fits
     if not color.CS_MAP[space].EXTENDED_RANGE:
-        if not color.in_gamut():
+        if not color.in_gamut() and not preserve_nan_hue:
             color.fit()

     # Premultiply
     if premultiplied:
         premultiply(color)

-    # Normalize hue
-    if isinstance(color._space, Cylindrical):
+    # Normalize hue (only if not 'specified')
+    if hue != "specified" and isinstance(color._space, Cylindrical):
         name = cast(Cylindrical, color._space).hue_name()
         color.set(name, lambda h: cast(float, h % 360))


 def color_bezier_lerp(
     create: Type['Color'],
     colors: List[ColorInput],
     space: str,
     out_space: str,
     progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+    hue: str,
     premultiplied: bool,
     **kwargs: Any
 ) -> InterpolateBezier:
     """Bezier interpolation."""

     # Construct piecewise interpolation object
     stops = {}  # type: Any

     if space is None:
         space = create.INTERPOLATE

     if isinstance(colors[0], stop):
         current = create(colors[0].color)
         stops[0] = colors[0].stop
     elif not callable(colors[0]):
         current = create(colors[0])
         stops[0] = None
     else:
         raise ValueError('Cannot have an easing function as the first item in an interpolation list')

     if out_space is None:
         out_space = current.space()

     current.convert(space, in_place=True)
-    normalize_color(current, space, premultiplied)
+    normalize_color(current, space, premultiplied, hue)

     easing = None  # type: Any
     easings = []  # type: Any
-    coords = [current[:]]
+    all_colors = [current]

     i = 0
     for x in colors[1:]:

         # Normalize all colors as Piecewise objects
         if isinstance(x, stop):
             i += 1
             stops[i] = x.stop
             color = current._handle_color_input(x.color)
         elif callable(x):
             easing = x
             continue
         else:
             i += 1
             color = current._handle_color_input(x)
             stops[i] = None

         # Adjust to color to space and ensure it fits
         color = color.convert(space)
-        normalize_color(color, space, premultiplied)
-
-        # Create an entry interpolating the current color and the next color
-        coords.append(color[:])
+        normalize_color(color, space, premultiplied, hue)
+        all_colors.append(color)
         easings.append(easing if easing is not None else progress)

         # The "next" color is now the "current" color
         easing = None
         current = color

+    # Apply hue adjustments across all colors
+    adjust_bezier_hues(all_colors, hue)
+
+    # Create coordinate list after hue adjustments
+    coords = [color[:] for color in all_colors]
+
     i += 1
     if i < 2:
         raise ValueError('Need at least two colors to interpolate')

     # Calculate stops
     stops = calc_stops(stops, i)

     # Send the interpolation list along with the stop map to the Piecewise interpolator
     return InterpolateBezier(
         coords,
         current._space.channels,
         create,
         easings,
         stops,
         space,
         out_space,
         process_mapping(progress, current._space.CHANNEL_ALIASES),
         premultiplied
     )
diff --git a/coloraide/interpolate/piecewise.py b/coloraide/interpolate/piecewise.py
index 3bd2b84..cf2b9c3 100644
--- a/coloraide/interpolate/piecewise.py
+++ b/coloraide/interpolate/piecewise.py
@@ -93,50 +93,58 @@ class InterpolatePiecewise(Interpolator):

         channels = []
         for i, values in enumerate(zip(*colors)):
             c1, c2 = values
             name = self.names[i]
             if alg.is_nan(c1) and alg.is_nan(c2):
                 value = alg.NaN
             elif alg.is_nan(c1):
                 value = c2
             elif alg.is_nan(c2):
                 value = c1
             else:
                 progress = None
                 if isinstance(easing, Mapping):
                     progress = easing.get(name)
                     if progress is None:
                         progress = easing.get('all')
                 else:
                     progress = easing
                 t = alg.clamp(progress(p), 0.0, 1.0) if progress is not None else p
                 value = alg.lerp(c1, c2, t)
             channels.append(value)
         color = self.create(self.space, channels[:-1], channels[-1])
         if self.premultiplied:
             postdivide(color)
+
+        # Normalize hue if we're in a cylindrical space
+        if isinstance(color._space, Cylindrical):
+            hue_name = cast(Cylindrical, color._space).hue_name()
+            current_hue = color.get(hue_name)
+            if not alg.is_nan(current_hue):
+                color.set(hue_name, current_hue % 360)
+
         return color.convert(self.out_space, in_place=True) if self.out_space != color.space() else color

     def __call__(self, p: float) -> 'Color':
         """Interpolate."""

         percent = alg.clamp(p, 0.0, 1.0)
         if percent > self.end:
             percent = self.end
         elif percent < self.start:
             percent = self.start
         last = self.start
         for i, colors in enumerate(self.color_map, 1):
             s = self.stops[i]
             if percent <= s:
                 r = s - last
                 p2 = (percent - last) / r if r else 1
                 easing = self.easings[i - 1]  # type: Any
                 if easing is None:
                     easing = self.progress
                 return self.interpolate(colors, easing, p2)
             last = s

         # We shouldn't ever hit this, but provided for typing.
         # If we do hit this, it would be a bug.
         raise RuntimeError('Iterpolation could not be found for {}'.format(percent))  # pragma: no cover
diff --git a/debug_adjustment.py b/debug_adjustment.py
new file mode 100644
index 0000000..2062257
--- /dev/null
+++ b/debug_adjustment.py
@@ -0,0 +1,32 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+from coloraide.interpolate.piecewise import adjust_hues
+
+# Test case 1
+print("=== Case 1 ===")
+c1 = Color('lch(75% 50 40)')
+c2 = Color('lch(30% 30 350)').mask("hue", invert=True)
+print(f"Before adjust_hues: c1={c1['hue']}, c2={c2['hue']}")
+
+# Apply the adjust logic manually (simulating what happens internally)
+c2_full = Color('lch(30% 30 350)')  # Original unmasked color for hue adjustment
+c1_copy = c1.clone()
+c2_copy = c2_full.clone()
+adjust_hues(c1_copy, c2_copy, "shorter")
+print(f"After adjust_hues: c1={c1_copy['hue']}, c2={c2_copy['hue']}")
+print(f"Mid-point: {(c1_copy['hue'] + c2_copy['hue']) / 2}")
+print()
+
+# Test case 2
+print("=== Case 2 ===")
+c1 = Color('lch(30% 30 350)')
+c2 = Color('lch(75% 50 40)').mask("hue", invert=True)
+print(f"Before adjust_hues: c1={c1['hue']}, c2={c2['hue']}")
+
+c2_full = Color('lch(75% 50 40)')  # Original unmasked color for hue adjustment
+c1_copy = c1.clone()
+c2_copy = c2_full.clone()
+adjust_hues(c1_copy, c2_copy, "shorter")
+print(f"After adjust_hues: c1={c1_copy['hue']}, c2={c2_copy['hue']}")
+print(f"Mid-point: {(c1_copy['hue'] + c2_copy['hue']) / 2}")
diff --git a/debug_both_cases.py b/debug_both_cases.py
new file mode 100644
index 0000000..d7e1a2a
--- /dev/null
+++ b/debug_both_cases.py
@@ -0,0 +1,22 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+print("=== Case 1: c2 - c1 > 180 ===")
+c1 = Color('lch(75% 50 40)')
+c2 = Color('lch(30% 30 350)')
+result1 = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
+print(f"c1: {c1}")
+print(f"c2 masked: {c2.mask('hue', invert=True)}")
+print(f"Result: {result1}")
+print(f"Expected: lch(75% 50 15)")
+print()
+
+print("=== Case 2: c2 - c1 < -180 ===")
+c1 = Color('lch(30% 30 350)')
+c2 = Color('lch(75% 50 40)')
+result2 = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
+print(f"c1: {c1}")
+print(f"c2 masked: {c2.mask('hue', invert=True)}")
+print(f"Result: {result2}")
+print(f"Expected: lch(30% 30 375)")
diff --git a/debug_color_creation.py b/debug_color_creation.py
new file mode 100644
index 0000000..a668b58
--- /dev/null
+++ b/debug_color_creation.py
@@ -0,0 +1,22 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+# Test color creation with different hue values
+c1 = Color("lch(75% 50 15)")
+c2 = Color("lch(75% 50 375)")
+c3 = Color("lch(30% 30 375)")
+
+print(f"lch(75% 50 15): {c1}")
+print(f"lch(75% 50 375): {c2}")
+print(f"lch(30% 30 375): {c3}")
+
+print(f"c1 hue: {c1['hue']}")
+print(f"c2 hue: {c2['hue']}")
+print(f"c3 hue: {c3['hue']}")
+
+print(f"c1 == c2: {c1 == c2}")
+
+# Test if normalization happens in color space conversion
+c4 = Color("srgb", [1, 0, 0]).convert("lch")
+print(f"Red in LCH: {c4}")
diff --git a/debug_convert.py b/debug_convert.py
new file mode 100644
index 0000000..dfd7fb0
--- /dev/null
+++ b/debug_convert.py
@@ -0,0 +1,19 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+# Test conversion
+c = Color("lch", [75, 50, 375])
+print(f"Original: {c}")
+
+# Convert to itself
+c2 = c.convert("lch")
+print(f"Convert to self: {c2}")
+
+# Convert to another space and back
+c3 = c.convert("lab").convert("lch")
+print(f"Lab round-trip: {c3}")
+
+# Convert to RGB and back
+c4 = c.convert("srgb").convert("lch")
+print(f"RGB round-trip: {c4}")
diff --git a/debug_coords.py b/debug_coords.py
new file mode 100644
index 0000000..8b80c46
--- /dev/null
+++ b/debug_coords.py
@@ -0,0 +1,25 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+from coloraide.interpolate import color_bezier_lerp
+
+# Test the specific case
+c1 = Color('hsl(250 50% 30%)')
+c2 = Color('hsl(none 0% 110%)')
+
+print(f"c1: {c1}")
+print(f"c2: {c2}")
+print(f"c1 coords: {c1[:]}")
+print(f"c2 coords: {c2[:]}")
+
+# Test with manual bezier call
+colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
+bezier_interp = color_bezier_lerp(
+    Color, colors, 'hsl', 'hsl',
+    progress=None, hue='shorter', premultiplied=True
+)
+print(f"Bezier coords: {bezier_interp.coordinates}")
+
+result = bezier_interp(0.75)
+print(f"Result: {result}")
+print(f"Result coords: {result[:]}")
diff --git a/debug_fit.py b/debug_fit.py
new file mode 100644
index 0000000..2db6205
--- /dev/null
+++ b/debug_fit.py
@@ -0,0 +1,14 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+# Test the fit function directly
+c2 = Color('hsl(none 0% 110%)')
+print(f"Before fit: {c2} -> {c2[:]}")
+print(f"In gamut: {c2.in_gamut()}")
+
+if not c2.in_gamut():
+    c2.fit()
+    print(f"After fit: {c2} -> {c2[:]}")
+else:
+    print("Already in gamut")
diff --git a/debug_hue.py b/debug_hue.py
new file mode 100644
index 0000000..84611ea
--- /dev/null
+++ b/debug_hue.py
@@ -0,0 +1,21 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+# Test the specific case
+colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
+result = Color.interpolate(colors, space='hsl', method='bezier')(0.75)
+print(f"Result: {result}")
+print(f"Expected: hsl(332.5 12.5% 82.5%)")
+
+# Let's also test with linear interpolation
+result_linear = Color.interpolate(colors, space='hsl', method='linear')(0.75)
+print(f"Linear result: {result_linear}")
+
+# Check the colors individually
+c1 = Color('hsl(250 50% 30%)')
+c2 = Color('hsl(none 0% 110%)')
+print(f"c1: {c1}")
+print(f"c2: {c2}")
+print(f"c1 hue: {c1['hue']}")
+print(f"c2 hue: {c2['hue']}")
diff --git a/debug_hue_short.py b/debug_hue_short.py
new file mode 100644
index 0000000..09bee34
--- /dev/null
+++ b/debug_hue_short.py
@@ -0,0 +1,19 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+# Test the first failing case
+c1 = Color('lch(75% 50 40)')
+c2 = Color('lch(30% 30 350)')
+
+print(f"c1: {c1}")
+print(f"c2: {c2}")
+print(f"c2.mask('hue', invert=True): {c2.mask('hue', invert=True)}")
+
+result = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
+print(f"Result: {result}")
+print(f"Expected: lch(75% 50 15)")
+
+# Let's also test without masking
+result_no_mask = c1.mix(c2, 0.50, hue="shorter", space="lch")
+print(f"Without mask: {result_no_mask}")
diff --git a/debug_hue_steps.py b/debug_hue_steps.py
new file mode 100644
index 0000000..8cd6571
--- /dev/null
+++ b/debug_hue_steps.py
@@ -0,0 +1,26 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+# Test step by step
+c1 = Color('lch(75% 50 40)')
+c2_masked = Color('lch(30% 30 350)').mask("hue", invert=True)
+
+print(f"c1: {c1}")
+print(f"c2_masked: {c2_masked}")
+
+# Manual interpolation calculation
+# After hue adjustment: c1=40+360=400, c2=350
+# At t=0.5: 400 * 0.5 + 350 * 0.5 = 375
+
+# Create the result manually
+manual_result = Color('lch', [75, 50, 375])
+print(f"Manual result (375°): {manual_result}")
+
+# Normalize manually
+manual_result_norm = Color('lch', [75, 50, 375 % 360])
+print(f"Manual result normalized (15°): {manual_result_norm}")
+
+# Test actual interpolation
+result = c1.mix(c2_masked, 0.50, hue="shorter", space="lch")
+print(f"Actual result: {result}")
diff --git a/debug_linear.py b/debug_linear.py
new file mode 100644
index 0000000..5647da6
--- /dev/null
+++ b/debug_linear.py
@@ -0,0 +1,20 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+from coloraide.interpolate import color_piecewise_lerp
+
+# Test the specific case with linear interpolation
+colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
+linear_interp = color_piecewise_lerp(
+    Color, colors, 'hsl', 'hsl',
+    progress=None, hue='shorter', premultiplied=True
+)
+print(f"Linear color_map: {linear_interp.color_map}")
+
+result = linear_interp(0.75)
+print(f"Linear result: {result}")
+
+# Let's also test intermediate values
+for t in [0.0, 0.25, 0.5, 0.75, 1.0]:
+    result = linear_interp(t)
+    print(f"t={t}: {result}")
diff --git a/debug_normalize.py b/debug_normalize.py
new file mode 100644
index 0000000..7b17d03
--- /dev/null
+++ b/debug_normalize.py
@@ -0,0 +1,18 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+from coloraide.interpolate.bezier import normalize_color
+from coloraide.spaces import Cylindrical
+
+# Test the normalize_color function directly
+c2 = Color('hsl(none 0% 110%)')
+print(f"Before normalize (specified): {c2} -> {c2[:]}")
+
+# Check if it's cylindrical
+print(f"Is cylindrical: {isinstance(c2._space, Cylindrical)}")
+if isinstance(c2._space, Cylindrical):
+    print(f"Hue name: {c2._space.hue_name()}")
+    print(f"Hue value: {c2.get('hue')}")
+
+normalize_color(c2, 'hsl', True, 'specified')
+print(f"After normalize (specified): {c2} -> {c2[:]}")
diff --git a/debug_second_case.py b/debug_second_case.py
new file mode 100644
index 0000000..5ceff81
--- /dev/null
+++ b/debug_second_case.py
@@ -0,0 +1,14 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+
+print("=== Case 2 from test_hue_shorter_cases ===")
+c1 = Color('lch(30% 30 350)')
+c2 = Color('lch(75% 50 40)')
+result2 = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
+print(f"Result: {result2}")
+print(f"Expected: lch(30% 30 375)")
+
+expected = Color("lch(30% 30 375)")
+print(f"Expected object: {expected}")
+print(f"Match: {result2.to_string() == expected.to_string()}")
diff --git a/debug_specified.py b/debug_specified.py
new file mode 100644
index 0000000..881dc91
--- /dev/null
+++ b/debug_specified.py
@@ -0,0 +1,15 @@
+#!/usr/bin/env python3
+
+from coloraide import Color
+from coloraide.interpolate import color_bezier_lerp
+
+# Test the specific case with specified hue
+colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
+bezier_interp = color_bezier_lerp(
+    Color, colors, 'hsl', 'hsl',
+    progress=None, hue='specified', premultiplied=True
+)
+print(f"Bezier coords with specified: {bezier_interp.coordinates}")
+
+result = bezier_interp(0.75)
+print(f"Result with specified: {result}")
Unstaged changes after reset:
M	coloraide/interpolate/__init__.py
M	coloraide/interpolate/bezier.py
M	coloraide/interpolate/piecewise.py
