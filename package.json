{"name": "coloraide", "version": "0.1.0", "description": "Coloraide documentation assest builder", "repository": "https://github.com/facelessuser/coloraide.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "type": "module", "scripts": {"build": "node_modules/.bin/gulp build --compress --lint --buildmkdocs --revision --sourcemaps", "compile": "node_modules/.bin/gulp build --compress --buildmkdocs --revision --sourcemaps", "clean_all": "node_modules/.bin/gulp clean", "serve": "node_modules/.bin/gulp serve", "lint": "node_modules/.bin/gulp lint"}, "engines": {"node": ">= 10"}, "devDependencies": {"@babel/cli": "^7.18.6", "@babel/core": "^7.18.6", "@babel/eslint-parser": "^7.18.2", "@babel/plugin-external-helpers": "^7.18.6", "@babel/preset-env": "^7.18.6", "@babel/register": "^7.18.6", "@fortawesome/fontawesome-free": "^6.1.1", "@mdi/svg": "^6.9.96", "@primer/octicons": "^17.3.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-replace": "^4.0.0", "autoprefixer": "^10.4.7", "babel-core": "^7.0.0-bridge.0", "clean-css": "^5.3.0", "css-mqpacker": "^7.0.0", "del": "^6.1.1", "eslint": "^8.19.0", "fast-glob": "^3.2.11", "gulp": "^4.0.2", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-eslint": "^6.0.0", "gulp-if": "^3.0.0", "gulp-postcss": "^9.0.1", "gulp-replace": "^1.1.3", "gulp-rev": "^9.0.0", "gulp-rev-replace": "^0.4.4", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "gulp-stylelint": "^13.0.0", "gulp-touch-fd": "github:funkedigital/gulp-touch-fd", "material-design-color": "^2.3.2", "material-shadows": "^3.0.1", "postcss-inline-svg": "^5.0.0", "postcss-pseudo-classes": "^0.2.1", "postcss-svgo": "^5.1.0", "promise": "^8.1.0", "rollup": "^2.76.0", "rollup-plugin-output-manifest": "^2.0.0", "rollup-plugin-regenerator": "^0.6.0", "rollup-plugin-terser": "^7.0.2", "sass": "^1.53.0", "stylelint": "^14.9.1", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^26.0.0", "stylelint-order": "^5.0.0", "stylelint-scss": "^4.3.0", "terser": "^5.14.1", "vinyl-paths": "^4.0.0", "yargs": "^17.5.1"}}