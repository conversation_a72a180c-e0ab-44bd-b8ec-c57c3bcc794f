[tox]
isolated_build = true
envlist=
    py37,py38,py39,py310,
    lint

[testenv]
passenv=LANG
deps=
    -rrequirements/project.txt
    -rrequirements/test.txt
commands=
    "{envbindir}"/mypy
    "{envbindir}"/py.test --cov coloraide --cov-append tests
    "{envbindir}"/coverage html -d {envtmpdir}/coverage
    "{envbindir}"/coverage xml
    "{envbindir}"/coverage report --show-missing

[testenv:lint]
deps=
    -rrequirements/lint.txt
commands=
    "{envbindir}"/flake8 .

[testenv:documents]
deps=
    -rrequirements/docs.txt
commands=
    "{envpython}" tools/buildwheel.py
    "{envpython}" -m mkdocs build --clean --verbose --strict
    "{envbindir}"/pyspelling

[flake8]
exclude=build/*,.tox/*,dist/*
max-line-length=120
ignore=D202,D203,D401,N802,N801,N803,N806,E741,W504,E743,D403,N818
