matrix:
- name: mkdocs
  sources:
  - site/**/*.html
  hunspell:
    d: docs/src/dictionary/hunspell/en_US
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/mkdocs.dic
  pipeline:
  - pyspelling.filters.html:
      comments: false
      attributes:
      - title
      - alt
      ignores:
      - 'code, pre, a.magiclink, span.keys, textarea, sub, sup'
      - '.MathJax_Preview, .md-nav__link, .md-footer-custom-text, .md-source__repository, .headerlink, .md-icon'
      - '.md-social__link'
      - '.swatch[title]'
  - pyspelling.filters.url:

- name: markdown
  sources:
  - README.md
  hunspell:
    d: docs/src/dictionary/hunspell/en_US
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/mkdocs.dic
  pipeline:
  - pyspelling.filters.markdown:
      markdown_extensions:
      - pymdownx.superfences:
      - pymdownx.highlight:
      - pymdownx.striphtml:
      - pymdownx.magiclink:
  - pyspelling.filters.html:
      comments: false
      attributes:
      - title
      - alt
      ignores:
      - code
      - pre
  - pyspelling.filters.url:

- name: python
  sources:
  - setup.py
  - "{coloraide,tests,tools}/**/*.py"
  hunspell:
    d: docs/src/dictionary/hunspell/en_US
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/python.dic
  pipeline:
  - pyspelling.filters.python:
      group_comments: True
  - pyspelling.flow_control.wildcard:
      allow:
      - py-comment
  - pyspelling.filters.context:
      context_visible_first: true
      delimiters:
      # Ignore lint (noqa) and coverage (pragma) as well as shebang (#!)
      - open: '^(?: *(?:noqa\b|pragma: no cover|type: .*?)|!)'
        close: '$'
      # Ignore Python encoding string -*- encoding stuff -*-
      - open: '^ *-\*-'
        close: '-\*-$'
  - pyspelling.filters.context:
      context_visible_first: true
      escapes: '\\[\\`]'
      delimiters:
      # Ignore multiline content between fences (fences can have 3 or more back ticks)
      # ```
      # content
      # ```
      - open: '(?s)^(?P<open> *`{3,})$'
        close: '^(?P=open)$'
      # Ignore text between inline back ticks
      - open: '(?P<open>`+)'
        close: '(?P=open)'
  - pyspelling.filters.url:
