{
  "extends": "eslint:recommended",
  "env": {
    "browser": true,
    "es6": true,
    "node": true
  },
  "globals": {
    "mermaid": true,
    "mermaidConfig": true
  },
  "parser": "@babel/eslint-parser",
  "parserOptions": {
    "ecmaVersion": 2020,
    "impliedStrict": true,
    "sourceType": "module",
    "ecmaFeatures": {
      "arrowFunctions": true,
      "binaryLiterals": true,
      "blockBindings": true,
      "classes": true,
      "defaultParams": true,
      "destructuring": true,
      "forOf": true,
      "generators": true,
      "globalReturn": true,
      "modules": true,
      "objectLiteralComputedProperties": true,
      "objectLiteralDuplicateProperties": true,
      "objectLiteralShorthandMethods": true,
      "objectLiteralShorthandProperties": true,
      "octalLiterals": true,
      "regexUFlag": true,
      "regexYFlag": true,
      "spread": true,
      "superInFunctions": false,
      "templateStrings": true,
      "unicodeCodePointEscapes": true
    }
  },
  "plugins": [],
  "rules": {
    "array-callback-return": 2,
    "array-bracket-spacing": 2,
    "arrow-parens": [2, "as-needed"],
    "arrow-spacing": 2,
    "block-scoped-var": 2,
    "block-spacing": 2,
    "brace-style": 2,
    "camelcase": [2, {
      "properties": "always"
    }],
    "comma-dangle": [2, "never"],
    "comma-style": [2, "last"],
    "complexity": 2,
    "computed-property-spacing": 2,
    "consistent-this": 2,
    "dot-notation": 2,
    "eol-last": 2,
    "eqeqeq": 2,
    "func-call-spacing": 2,
    "func-names": [2, "never"],
    "func-style": 2,
    "generator-star-spacing": 2,
    "indent": [2, 2, {
      "FunctionDeclaration": {
        "body": 1,
        "parameters": 2
      },
      "FunctionExpression": {
        "body": 1,
        "parameters": 2
      },
      "MemberExpression": 1,
      "VariableDeclarator": {
        "var": 2,
        "let": 2,
        "const": 3
      },
      "ignoredNodes": [
        "TemplateLiteral"
      ],
      "SwitchCase": 1
    }],
    "init-declarations": 2,
    "key-spacing": 2,
    "keyword-spacing": 2,
    "linebreak-style": 0,
    "lines-around-comment": 2,
    "lines-around-directive": 2,
    "max-depth": 2,
    "max-len": [1, {
      "code": 120,
      "ignorePattern": "\/\/ TODO"
    }],
    "max-nested-callbacks": 2,
    "max-params": 2,
    "max-statements-per-line": 2,
    "new-cap": 2,
    "newline-per-chained-call": 2,
    "no-array-constructor": 2,
    "no-alert": 2,
    "no-caller": 2,
    "no-confusing-arrow": [2, {
      "allowParens": false
    }],
    "no-console": 1,
    "no-duplicate-imports": 2,
    "no-eq-null": 2,
    "no-eval": 2,
    "no-extend-native": 2,
    "no-extra-bind": 2,
    "no-floating-decimal": 2,
    "no-global-assign": 2,
    "no-invalid-this": 2,
    "no-labels": 2,
    "no-lone-blocks": 2,
    "no-loop-func": 2,
    "no-multiple-empty-lines": [1, {
      "max": 1
    }],
    "no-nested-ternary": 2,
    "no-new": 0,
    "no-new-object": 2,
    "no-param-reassign": 2,
    "no-prototype-builtins": 2,
    "no-shadow": 2,
    "no-tabs": 2,
    "no-template-curly-in-string": 2,
    "no-throw-literal": 2,
    "no-trailing-spaces": 2,
    "no-undefined": 2,
    "no-underscore-dangle": [2, {
      "allowAfterThis": true,
      "allowAfterSuper": true
    }],
    "no-unneeded-ternary": 2,
    "no-unsafe-negation": 2,
    "no-unused-expressions": 2,
    "no-unused-vars": 1,
    "no-use-before-define": 2,
    "no-useless-call": 2,
    "no-useless-computed-key": 2,
    "no-useless-constructor": 2,
    "no-useless-rename": 2,
    "no-var": 2,
    "no-whitespace-before-property": 2,
    "no-with": 2,
    "object-curly-spacing": [2, "never"],
    "object-shorthand": 2,
    "one-var-declaration-per-line": 2,
    "operator-assignment": 2,
    "prefer-arrow-callback": 2,
    "prefer-const": 2,
    "prefer-numeric-literals": 2,
    "prefer-spread": 2,
    "prefer-template": 2,
    // "quotes": [2, "double"],
    "radix": 2,
    "require-jsdoc": [1, {
      "require": {
        "FunctionDeclaration": true,
        "MethodDefinition": true,
        "ClassDeclaration": false
      }
    }],
    "rest-spread-spacing": 2,
    "semi": [2, "never"],
    "semi-spacing": 2,
    "space-before-function-paren": [2, "never"],
    "space-in-parens": 2,
    "space-infix-ops": 2,
    "space-unary-ops": 2,
    "spaced-comment": [2, "always", {
      "line": {
        "markers": ["/", ":"],
        "exceptions": ["-", "+"]
      },
      "block": {
        "markers": ["!"],
        "exceptions": ["*"],
        "balanced": true
      }
    }],
    "strict": 2,
    "template-curly-spacing": "off",
    "unicode-bom": 2,
    "valid-jsdoc": [1, {
      "prefer": {
        "arg": "param",
        "argument": "param",
        "class": "constructor",
        "returns": "return",
        "virtual": "abstract"
      },
      "requireParamDescription": true,
      "requireReturn": false,
      "requireReturnDescription": true
    }],
    "yield-star-spacing": 2
  },
  "root": true
}
