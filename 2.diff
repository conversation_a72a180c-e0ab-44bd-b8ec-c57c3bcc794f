diff --git a/coloraide/color.py b/coloraide/color.py
index 18644c3..097c5d6 100644
--- a/coloraide/color.py
+++ b/coloraide/color.py
@@ -31,112 +31,116 @@ from .spaces.a98_rgb import A98RGB
 from .spaces.a98_rgb_linear import A98RGBLinear
 from .spaces.prophoto_rgb import ProPhotoRGB
 from .spaces.prophoto_rgb_linear import ProPhotoRGBLinear
 from .spaces.rec2020 import Rec2020
 from .spaces.rec2020_linear import Rec2020Linear
 from .spaces.xyz_d65 import XYZD65
 from .spaces.xyz_d50 import XYZD50
 from .spaces.oklab.css import Oklab
 from .spaces.oklch.css import OkLCh
 from .distance import DeltaE
 from .distance.delta_e_76 import DE76
 from .distance.delta_e_94 import DE94
 from .distance.delta_e_cmc import DECMC
 from .distance.delta_e_2000 import DE2000
 from .distance.delta_e_hyab import <PERSON>HyAB
 from .distance.delta_e_ok import DEOK
 from .contrast import ColorContrast
 from .contrast.wcag21 import WCAG21Contrast
 from .gamut import Fit
 from .gamut.fit_lch_chroma import LChChroma
 from .gamut.fit_oklch_chroma import OkLChChroma
 from .cat import CAT, Bradford
 from .filters import Filter
 from .filters.w3c_filter_effects import Sepia, Brightness, Contrast, Saturate, Opacity, HueRotate, Grayscale, Invert
 from .filters.cvd import Protan, Deutan, Tritan
+from .interpolate.piecewise import InterpolatePiecewise
+from .interpolate.bezier import InterpolateBezier
 from .types import Plugin
 from typing import overload, Union, Sequence, Dict, List, Optional, Any, cast, Callable, Tuple, Type, Mapping


 class ColorMatch:
     """Color match object."""

     def __init__(self, color: 'Color', start: int, end: int) -> None:
         """Initialize."""

         self.color = color
         self.start = start
         self.end = end

     def __str__(self) -> str:  # pragma: no cover
         """String."""

         return "ColorMatch(color={!r}, start={}, end={})".format(self.color, self.start, self.end)

     __repr__ = __str__


 class ColorMeta(abc.ABCMeta):
     """Ensure on subclass that the subclass has new instances of mappings."""

     def __init__(cls, name: str, bases: Tuple[object, ...], clsdict: Dict[str, Any]) -> None:
         """Copy mappings on subclass."""

         # Ensure subclassed Color objects do not use the same plugin mappings
         if len(cls.mro()) > 2:
             cls.CS_MAP = cls.CS_MAP.copy()  # type: Dict[str, Space]
             cls.DE_MAP = cls.DE_MAP.copy()  # type: Dict[str, DeltaE]
             cls.FIT_MAP = cls.FIT_MAP.copy()  # type: Dict[str, Fit]
             cls.CAT_MAP = cls.CAT_MAP.copy()  # type: Dict[str, CAT]
             cls.FILTER_MAP = cls.FILTER_MAP.copy()  # type: Dict[str, Filter]
             cls.CONTRAST_MAP = cls.CONTRAST_MAP.copy()  # type: Dict[str, ColorContrast]
+            cls.INTERPOLATE_MAP = cls.INTERPOLATE_MAP.copy()  # type: Dict[str, Any]

         # Ensure each derived class tracks its own conversion paths for color spaces
         # relative to the installed color space plugins.
         @classmethod  # type: ignore[misc]
         @functools.lru_cache(maxsize=256)
         def _get_convert_chain(
             cls: Type['Color'],
             space: 'Space',
             target: str
         ) -> List[Tuple['Space', 'Space', int, bool]]:
             """Resolve a conversion chain, cache it for speed."""

             return convert.get_convert_chain(cls, space, target)

         cls._get_convert_chain = _get_convert_chain


 class Color(metaclass=ColorMeta):
     """Color class object which provides access and manipulation of color spaces."""

     CS_MAP = {}  # type: Dict[str, Space]
     DE_MAP = {}  # type: Dict[str, DeltaE]
     FIT_MAP = {}  # type: Dict[str, Fit]
     CAT_MAP = {}  # type: Dict[str, CAT]
     CONTRAST_MAP = {}  # type: Dict[str, ColorContrast]
     FILTER_MAP = {}  # type: Dict[str, Filter]
+    INTERPOLATE_MAP = {}  # type: Dict[str, Any]
     PRECISION = util.DEF_PREC
     FIT = util.DEF_FIT
     INTERPOLATE = util.DEF_INTERPOLATE
     DELTA_E = util.DEF_DELTA_E
     HARMONY = util.DEF_HARMONY
     CHROMATIC_ADAPTATION = 'bradford'
     CONTRAST = 'wcag21'

     # It is highly unlikely that a user would ever need to override this, but
     # just in case, it is exposed, but undocumented.
     #
     # This is meant to prevent infinite loops in the event that a user registers
     # poorly crafted color spaces with circular convert linkage or somehow doesn't
     # resolve to XYZ. 10 is a generous size as our current largest iteration chain
     # is 6, and increasing that past 10 seems highly unlikely:
     #    XYZ -> sRGB Linear -> sRGB -> HSL -> HSV -> HWB
     _MAX_CONVERT_ITERATIONS = 10

     def __init__(
         self,
         color: ColorInput,
         data: Optional[VectorLike] = None,
         alpha: float = util.DEF_ALPHA,
         **kwargs: Any
     ) -> None:
@@ -304,107 +308,113 @@ class Color(metaclass=ColorMeta):

         if not isinstance(plugin, Sequence):
             plugin = [plugin]

         mapping = None  # type: Optional[Dict[str, Any]]
         for p in plugin:
             if isinstance(p, Space):
                 mapping = cls.CS_MAP
                 reset_convert_cache = True
             elif isinstance(p, DeltaE):
                 mapping = cls.DE_MAP
             elif isinstance(p, CAT):
                 mapping = cls.CAT_MAP
             elif isinstance(p, Filter):
                 mapping = cls.FILTER_MAP
             elif isinstance(p, ColorContrast):
                 mapping = cls.CONTRAST_MAP
             elif isinstance(p, Fit):
                 mapping = cls.FIT_MAP
                 if p.NAME == 'clip':
                     if reset_convert_cache:  # pragma: no cover
                         cls._get_convert_chain.cache_clear()
                     if not silent:
                         raise ValueError("'{}' is a reserved name for gamut mapping/reduction and cannot be overridden")
                     continue  # pragma: no cover
+            elif hasattr(p, '__call__') and hasattr(p, 'NAME') and isinstance(p, Plugin):
+                # This is an interpolation plugin or similar callable plugin
+                mapping = cls.INTERPOLATE_MAP
             else:
                 if reset_convert_cache:  # pragma: no cover
                     cls._get_convert_chain.cache_clear()
                 raise TypeError("Cannot register plugin of type '{}'".format(type(p)))

             name = p.NAME
             value = p

             if name != "*" and name not in mapping or overwrite:
                 cast(Dict[str, Plugin], mapping)[name] = value
             elif not silent:
                 if reset_convert_cache:  # pragma: no cover
                     cls._get_convert_chain.cache_clear()
                 raise ValueError("A plugin with the name of '{}' already exists or is not allowed".format(name))

         if reset_convert_cache:
             cls._get_convert_chain.cache_clear()

     @classmethod
     def deregister(cls, plugin: Union[str, Sequence[str]], *, silent: bool = False) -> None:
         """Deregister a plugin by name of specified plugin type."""

         reset_convert_cache = False

         if isinstance(plugin, str):
             plugin = [plugin]

         mapping = None  # type: Optional[Dict[str, Any]]
         for p in plugin:
             if p == '*':
                 cls.CS_MAP.clear()
                 cls.DE_MAP.clear()
                 cls.FIT_MAP.clear()
                 cls.CAT_MAP.clear()
                 cls.CONTRAST_MAP.clear()
+                cls.INTERPOLATE_MAP.clear()
                 return

             ptype, name = p.split(':', 1)
             if ptype == 'space':
                 mapping = cls.CS_MAP
                 reset_convert_cache = True
             elif ptype == "delta-e":
                 mapping = cls.DE_MAP
             elif ptype == 'cat':
                 mapping = cls.CAT_MAP
             elif ptype == 'filter':
                 mapping = cls.FILTER_MAP
             elif ptype == 'contrast':
                 mapping = cls.CONTRAST_MAP
             elif ptype == "fit":
                 mapping = cls.FIT_MAP
                 if name == 'clip':
                     if reset_convert_cache:  # pragma: no cover
                         cls._get_convert_chain.cache_clear()
                     if not silent:
                         raise ValueError("'{}' is a reserved name gamut mapping/reduction and cannot be removed")
                     continue  # pragma: no cover
+            elif ptype == "interpolate":
+                mapping = cls.INTERPOLATE_MAP
             else:
                 if reset_convert_cache:  # pragma: no cover
                     cls._get_convert_chain.cache_clear()
                 raise ValueError("The plugin category of '{}' is not recognized".format(ptype))

             if name == '*':
                 mapping.clear()
             elif name in mapping:
                 del mapping[name]
             elif not silent:
                 if reset_convert_cache:
                     cls._get_convert_chain.cache_clear()
                 raise ValueError("A plugin of name '{}' under category '{}' could not be found".format(name, ptype))

         if reset_convert_cache:
             cls._get_convert_chain.cache_clear()

     @classmethod
     def random(cls, space: str, *, limits: Optional[Sequence[Optional[Sequence[float]]]] = None) -> 'Color':
         """Get a random color."""

         # Get the color space and number of channels
         cs = cls.CS_MAP[space]
         num_chan = len(cs.CHANNELS)

@@ -718,51 +728,51 @@ class Color(metaclass=ColorMeta):

     @classmethod
     def interpolate(
         cls,
         colors: Sequence[Union[ColorInput, interpolate.common.stop, Callable[..., float]]],
         *,
         space: Optional[str] = None,
         out_space: Optional[str] = None,
         progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]] = None,
         hue: str = util.DEF_HUE_ADJ,
         premultiplied: bool = True,
         method: str = "linear"
     ) -> interpolate.common.Interpolator:
         """
         Return an interpolation function.

         The function will return an interpolation function that accepts a value (which should
         be in the range of [0..1] and will return a color based on that value.

         While we use NaNs to mask off channels when doing the interpolation, we do not allow
         arbitrary specification of NaNs by the user, they must specify channels via `adjust`
         if they which to target specific channels for mixing. Null hues become NaNs before
         mixing occurs.
         """

-        return interpolate.get_interpolator(method)(
+        return interpolate.get_interpolator(method, cls)(
             cls,
             colors=colors,
             space=space,
             out_space=out_space,
             progress=progress,
             hue=hue,
             premultiplied=premultiplied
         )

     def filter(  # noqa: A003
         self,
         name: str,
         amount: Optional[float] = None,
         *,
         space: Optional[str] = None,
         in_place: bool = False,
         **kwargs: Any
     ) -> 'Color':
         """Filter."""

         return filters.filters(self, name, amount, space, in_place, **kwargs)

     def harmony(
         self,
         name: str,
@@ -905,28 +915,32 @@ Color.register(
         DE76(),
         DE94(),
         DECMC(),
         DE2000(),
         DEHyAB(),
         DEOK(),

         # Fit
         LChChroma(),
         OkLChChroma(),

         # Filters
         Sepia(),
         Brightness(),
         Contrast(),
         Saturate(),
         Opacity(),
         HueRotate(),
         Grayscale(),
         Invert(),
         Protan(),
         Deutan(),
         Tritan(),

         # Contrast
-        WCAG21Contrast()
+        WCAG21Contrast(),
+
+        # Interpolation
+        InterpolatePiecewise(),
+        InterpolateBezier()
     ]
 )
diff --git a/coloraide/interpolate/__init__.py b/coloraide/interpolate/__init__.py
index 0d9c383..a14f506 100644
--- a/coloraide/interpolate/__init__.py
+++ b/coloraide/interpolate/__init__.py
@@ -1,36 +1,48 @@
 """
 Interpolation methods.

 Originally, the base code for `interpolate`, `mix` and `steps` was ported from the
 https://colorjs.io project. Since that time, there has been significant modifications
 that add additional features etc. The base logic though is attributed to the original
 authors.

 In general, the logic mimics in many ways the `color-mix` function as outlined in the Level 5
 color draft (Oct 2020), but the initial approach was modeled directly off of the work done in
 color.js.
 ---
 Original Authors: <AUTHORS>
 License: MIT (As noted in https://github.com/LeaVerou/color.js/blob/master/package.json)
 """
 from .bezier import color_bezier_lerp
 from .piecewise import color_piecewise_lerp
-from .common import Interpolator, hint, stop  # noqa: F401
-from typing import Callable, Dict
+from .common import Interpolator, hint, stop, calc_stops  # noqa: F401
+from typing import Callable, Dict, TYPE_CHECKING

-__all__ = ('stop', 'hint', 'get_interpolator')
+if TYPE_CHECKING:  # pragma: no cover
+    from ..color import Color
+
+__all__ = ('stop', 'hint', 'get_interpolator', 'calc_stops')


 SUPPORTED = {
     "linear": color_piecewise_lerp,
     "bezier": color_bezier_lerp
 }  # type: Dict[str, Callable[..., Interpolator]]


-def get_interpolator(interpolator: str) -> Callable[..., Interpolator]:
-    """Get desired blend mode."""
+def get_interpolator(interpolator: str, color_class: 'Color' = None) -> Callable[..., Interpolator]:
+    """Get desired interpolation method."""

+    # Use plugin system if color_class is available
+    if color_class is not None and hasattr(color_class, 'INTERPOLATE_MAP'):
+        plugin = color_class.INTERPOLATE_MAP.get(interpolator)
+        if plugin is not None:
+            return plugin
+        # If plugin system is available but plugin not found, raise error immediately
+        raise ValueError("'{}' is not a recognized interpolator".format(interpolator))
+
+    # Fallback to hardcoded SUPPORTED dict (for backward compatibility)
     try:
         return SUPPORTED[interpolator]
     except KeyError:
         raise ValueError("'{}' is not a recognized interpolator".format(interpolator))
diff --git a/coloraide/interpolate/bezier.py b/coloraide/interpolate/bezier.py
index a0b4e90..2355ee8 100644
--- a/coloraide/interpolate/bezier.py
+++ b/coloraide/interpolate/bezier.py
@@ -1,55 +1,55 @@
 """Bezier interpolation."""
 from .. import algebra as alg
 from ..spaces import Cylindrical
-from ..types import Vector, ColorInput
+from ..types import Vector, ColorInput, Plugin
 from typing import Optional, Callable, Sequence, Mapping, Type, Dict, List, Union, cast, Any, TYPE_CHECKING
 from .common import stop, Interpolator, calc_stops, process_mapping, premultiply, postdivide

 if TYPE_CHECKING:  # pragma: no cover
     from ..color import Color


 def binomial_row(n: int) -> List[int]:
     """
     Binomial row.

     Return row in Pascal's triangle.
     """

     row = [1, 1]
     for i in range(n - 1):
         r = [1]
         x = 0
         for x in range(1, len(row)):
             r.append(row[x] + row[x - 1])
         r.append(row[x])
         row = r
     return row


-class InterpolateBezier(Interpolator):
+class BezierInterpolator(Interpolator):
     """Interpolate Bezier."""

     def __init__(
         self,
         coordinates: List[Vector],
         names: Sequence[str],
         create: Type['Color'],
         easings: List[Optional[Callable[..., float]]],
         stops: Dict[int, float],
         space: str,
         out_space: str,
         progress: Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]],
         premultiplied: bool
     ) -> None:
         """Initialize."""

         self.start = stops[0]
         self.end = stops[len(stops) - 1]
         self.stops = stops
         self.easings = easings
         self.coordinates = coordinates
         self.length = len(self.coordinates)
         self.names = names
         self.create = create
         self.progress = progress
@@ -123,126 +123,138 @@ class InterpolateBezier(Interpolator):
         """Interpolate."""

         percent = alg.clamp(p, 0.0, 1.0)
         if percent > self.end:
             percent = self.end
         elif percent < self.start:
             percent = self.start
         last = self.start
         for i in range(1, self.length):
             s = self.stops[i]
             if percent <= s:
                 r = s - last
                 p2 = (percent - last) / r if r else 1
                 easing = self.easings[i - 1]  # type: Any
                 if easing is None:
                     easing = self.progress
                 piece = 1 / (self.length - 1)
                 return self.interpolate(easing, p2, (i - 1) * piece, i * piece)
             last = s

         # We shouldn't ever hit this, but provided for typing.
         # If we do hit this, it would be a bug.
         raise RuntimeError('Iterpolation could not be found for {}'.format(percent))  # pragma: no cover


-def normalize_color(color: 'Color', space: str, premultiplied: bool) -> None:
+def normalize_color(color: 'Color', space: str, premultiplied: bool, hue: str) -> None:
     """Normalize color."""

     # Adjust to color to space and ensure it fits
     if not color.CS_MAP[space].EXTENDED_RANGE:
         if not color.in_gamut():
             color.fit()

     # Premultiply
     if premultiplied:
         premultiply(color)

-    # Normalize hue
-    if isinstance(color._space, Cylindrical):
+    # Normalize hue only if not using "specified" mode
+    if isinstance(color._space, Cylindrical) and hue != "specified":
         name = cast(Cylindrical, color._space).hue_name()
         color.set(name, lambda h: cast(float, h % 360))


 def color_bezier_lerp(
     create: Type['Color'],
     colors: List[ColorInput],
     space: str,
     out_space: str,
     progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
+    hue: str,
     premultiplied: bool,
     **kwargs: Any
-) -> InterpolateBezier:
+) -> BezierInterpolator:
     """Bezier interpolation."""

     # Construct piecewise interpolation object
     stops = {}  # type: Any

     if space is None:
         space = create.INTERPOLATE

     if isinstance(colors[0], stop):
         current = create(colors[0].color)
         stops[0] = colors[0].stop
     elif not callable(colors[0]):
         current = create(colors[0])
         stops[0] = None
     else:
         raise ValueError('Cannot have an easing function as the first item in an interpolation list')

     if out_space is None:
         out_space = current.space()

     current.convert(space, in_place=True)
-    normalize_color(current, space, premultiplied)
+    normalize_color(current, space, premultiplied, hue)

     easing = None  # type: Any
     easings = []  # type: Any
     coords = [current[:]]

     i = 0
     for x in colors[1:]:

         # Normalize all colors as Piecewise objects
         if isinstance(x, stop):
             i += 1
             stops[i] = x.stop
             color = current._handle_color_input(x.color)
         elif callable(x):
             easing = x
             continue
         else:
             i += 1
             color = current._handle_color_input(x)
             stops[i] = None

         # Adjust to color to space and ensure it fits
         color = color.convert(space)
-        normalize_color(color, space, premultiplied)
+        normalize_color(color, space, premultiplied, hue)

         # Create an entry interpolating the current color and the next color
         coords.append(color[:])
         easings.append(easing if easing is not None else progress)

         # The "next" color is now the "current" color
         easing = None
         current = color

     i += 1
     if i < 2:
         raise ValueError('Need at least two colors to interpolate')

     # Calculate stops
     stops = calc_stops(stops, i)

-    # Send the interpolation list along with the stop map to the Piecewise interpolator
-    return InterpolateBezier(
+    # Send the interpolation list along with the stop map to the Bezier interpolator
+    return BezierInterpolator(
         coords,
         current._space.channels,
         create,
         easings,
         stops,
         space,
         out_space,
         process_mapping(progress, current._space.CHANNEL_ALIASES),
         premultiplied
     )
+
+
+# Plugin class for bezier interpolation
+class InterpolateBezier(Plugin):
+    """Bezier interpolation plugin."""
+
+    NAME = 'bezier'
+
+    def __call__(self, *args, **kwargs):
+        """Call the bezier interpolation function."""
+        return color_bezier_lerp(*args, **kwargs)
diff --git a/coloraide/interpolate/piecewise.py b/coloraide/interpolate/piecewise.py
index 3bd2b84..7c5a650 100644
--- a/coloraide/interpolate/piecewise.py
+++ b/coloraide/interpolate/piecewise.py
@@ -1,29 +1,29 @@
 """Piecewise linear interpolation."""
 from .. import algebra as alg
 from ..spaces import Cylindrical
-from ..types import Vector, ColorInput
+from ..types import Vector, ColorInput, Plugin
 from typing import Optional, Callable, Sequence, Mapping, Type, Dict, List, Union, cast, Tuple, Any, TYPE_CHECKING
 from .common import stop, Interpolator, calc_stops, process_mapping, premultiply, postdivide

 if TYPE_CHECKING:  # pragma: no cover
     from ..color import Color


 def adjust_hues(color1: 'Color', color2: 'Color', hue: str) -> None:
     """Adjust hues."""

     if hue == "specified":
         return

     name = cast(Cylindrical, color1._space).hue_name()
     c1 = color1.get(name)
     c2 = color2.get(name)

     c1 = c1 % 360
     c2 = c2 % 360

     if alg.is_nan(c1) or alg.is_nan(c2):
         color1.set(name, c1)
         color2.set(name, c2)
         return

@@ -32,51 +32,51 @@ def adjust_hues(color1: 'Color', color2: 'Color', hue: str) -> None:
             c1 += 360
         elif c2 - c1 < -180:
             c2 += 360

     elif hue == "longer":
         if 0 < (c2 - c1) < 180:
             c1 += 360
         elif -180 < (c2 - c1) <= 0:
             c2 += 360

     elif hue == "increasing":
         if c2 < c1:
             c2 += 360

     elif hue == "decreasing":
         if c1 < c2:
             c1 += 360

     else:
         raise ValueError("Unknown hue adjuster '{}'".format(hue))

     color1.set(name, c1)
     color2.set(name, c2)


-class InterpolatePiecewise(Interpolator):
+class PiecewiseInterpolator(Interpolator):
     """Interpolate multiple ranges of colors."""

     def __init__(
         self,
         color_map: List[Tuple[Vector, Vector]],
         names: Sequence[str],
         create: Type['Color'],
         easings: List[Optional[Callable[..., float]]],
         stops: Dict[int, float],
         space: str,
         out_space: str,
         progress: Optional[Union[Callable[..., float], Mapping[str, Callable[..., float]]]],
         premultiplied: bool
     ):
         """Initialize."""

         self.start = stops[0]
         self.end = stops[len(stops) - 1]
         self.stops = stops
         self.color_map = color_map
         self.names = names
         self.create = create
         self.easings = easings
         self.space = space
         self.out_space = out_space
@@ -142,51 +142,51 @@ class InterpolatePiecewise(Interpolator):
         raise RuntimeError('Iterpolation could not be found for {}'.format(percent))  # pragma: no cover


 def normalize_color(color: 'Color', space: str, premultiplied: bool) -> None:
     """Normalize the color."""

     # Adjust to color to space and ensure it fits
     if not color.CS_MAP[space].EXTENDED_RANGE:
         if not color.in_gamut():
             color.fit()

     # Premultiply
     if premultiplied:
         premultiply(color)


 def color_piecewise_lerp(
     create: Type['Color'],
     colors: List[Union[ColorInput, stop, Callable[..., float]]],
     space: str,
     out_space: str,
     progress: Optional[Union[Mapping[str, Callable[..., float]], Callable[..., float]]],
     hue: str,
     premultiplied: bool,
     **kwargs: Any
-) -> InterpolatePiecewise:
+) -> PiecewiseInterpolator:
     """Piecewise Interpolation."""

     # Construct piecewise interpolation object
     stops = {}  # type: Any
     color_map = []

     if space is None:
         space = create.INTERPOLATE

     if isinstance(colors[0], stop):
         current = create(colors[0].color)
         stops[0] = colors[0].stop
     elif not callable(colors[0]):
         current = create(colors[0])
         stops[0] = None
     else:
         raise ValueError('Cannot have an easing function as the first item in an interpolation list')

     if out_space is None:
         out_space = current.space()

     current.convert(space, in_place=True)
     normalize_color(current, space, premultiplied)

     easing = None  # type: Any
@@ -211,36 +211,47 @@ def color_piecewise_lerp(
         # Adjust to color to space and ensure it fits
         color = color.convert(space)
         normalize_color(color, space, premultiplied)

         # Adjust hues if we have two valid hues
         color2 = color.clone()
         if isinstance(current._space, Cylindrical):
             adjust_hues(current, color2, hue)

         # Create an entry interpolating the current color and the next color
         color_map.append((current[:], color2[:]))
         easings.append(easing if easing is not None else progress)

         # The "next" color is now the "current" color
         easing = None
         current = color

     i += 1
     if i < 2:
         raise ValueError('Need at least two colors to interpolate')

     # Calculate stops
     stops = calc_stops(stops, i)

     # Send the interpolation list along with the stop map to the Piecewise interpolator
-    return InterpolatePiecewise(
+    return PiecewiseInterpolator(
         color_map,
         current._space.channels,
         create,
         easings,
         stops,
         space,
         out_space,
         process_mapping(progress, current._space.CHANNEL_ALIASES),
         premultiplied
     )
+
+
+# Plugin class for piecewise linear interpolation
+class InterpolatePiecewise(Plugin):
+    """Piecewise linear interpolation plugin."""
+
+    NAME = 'linear'
+
+    def __call__(self, *args, **kwargs):
+        """Call the piecewise linear interpolation function."""
+        return color_piecewise_lerp(*args, **kwargs)
Unstaged changes after reset:
M	coloraide/color.py
M	coloraide/interpolate/__init__.py
M	coloraide/interpolate/bezier.py
M	coloraide/interpolate/piecewise.py
