[build-system]
requires = [
    "hatchling>=0.21.1",
]
build-backend = "hatchling.build"

[project]
name = "coloraide"
description = "A color library for Python."
readme = "README.md"
license = "MIT"
requires-python = ">=3.6"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
keywords = [
    "css",
    "color",
]
dynamic = [
    "classifiers",
    "dependencies",
    "version",
]

[project.urls]
Homepage = "https://github.com/facelessuser/coloraide"

[tool.hatch.version]
source = "code"
path = "coloraide/__meta__.py"

[tool.hatch.build.targets.wheel]
include = [
    "/coloraide",
]

[tool.hatch.build.targets.sdist]
include = [
    "/docs/src/markdown/**/*.md",
    "/docs/src/markdown/**/*.gif",
    "/docs/src/markdown/**/*.png",
    "/docs/src/markdown/dictionary/*.txt",
    "/docs/theme/**/*.css",
    "/docs/theme/**/*.js",
    "/docs/theme/**/*.html",
    "/docs/theme/**/*.css.map",
    "/docs/theme/**/*.js.map",
    "/requirements/*.txt",
    "/coloraide/**/*.py",
    "/coloraide/py.typed",
    "/tests/**/*.py",
    "/tools/**/*.py",
    "/.pyspelling.yml",
    "/.coveragerc",
    "/mkdocs.yml",
    "/tox.ini",
]

[tool.mypy]
files = [
    "coloraide"
]
strict = true
show_error_codes = true

[tool.hatch.metadata.hooks.custom]
