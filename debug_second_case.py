#!/usr/bin/env python3

from coloraide import Color

print("=== Case 2 from test_hue_shorter_cases ===")
c1 = Color('lch(30% 30 350)')
c2 = Color('lch(75% 50 40)')
result2 = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
print(f"Result: {result2}")
print(f"Expected: lch(30% 30 375)")

expected = Color("lch(30% 30 375)")
print(f"Expected object: {expected}")
print(f"Match: {result2.to_string() == expected.to_string()}")
