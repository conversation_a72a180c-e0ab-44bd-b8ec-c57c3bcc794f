#!/usr/bin/env python3

from coloraide import Color
from coloraide.interpolate.bezier import normalize_color
from coloraide.spaces import Cylindrical

# Test the normalize_color function directly
c2 = Color('hsl(none 0% 110%)')
print(f"Before normalize (specified): {c2} -> {c2[:]}")

# Check if it's cylindrical
print(f"Is cylindrical: {isinstance(c2._space, Cylindrical)}")
if isinstance(c2._space, Cylindrical):
    print(f"Hue name: {c2._space.hue_name()}")
    print(f"Hue value: {c2.get('hue')}")

normalize_color(c2, 'hsl', True, 'specified')
print(f"After normalize (specified): {c2} -> {c2[:]}")
