#!/usr/bin/env python3

from coloraide import Color
from coloraide.interpolate import color_bezier_lerp

# Test the specific case with specified hue
colors = ['hsl(250 50% 30%)', 'hsl(none 0% 110%)']
bezier_interp = color_bezier_lerp(
    Color, colors, 'hsl', 'hsl',
    progress=None, hue='specified', premultiplied=True
)
print(f"Bezier coords with specified: {bezier_interp.coordinates}")

result = bezier_interp(0.75)
print(f"Result with specified: {result}")
