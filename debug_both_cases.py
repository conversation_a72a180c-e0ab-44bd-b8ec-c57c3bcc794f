#!/usr/bin/env python3

from coloraide import Color

print("=== Case 1: c2 - c1 > 180 ===")
c1 = Color('lch(75% 50 40)')
c2 = Color('lch(30% 30 350)')
result1 = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
print(f"c1: {c1}")
print(f"c2 masked: {c2.mask('hue', invert=True)}")
print(f"Result: {result1}")
print(f"Expected: lch(75% 50 15)")
print()

print("=== Case 2: c2 - c1 < -180 ===")
c1 = Color('lch(30% 30 350)')
c2 = Color('lch(75% 50 40)')
result2 = c1.mix(c2.mask("hue", invert=True), 0.50, hue="shorter", space="lch")
print(f"c1: {c1}")
print(f"c2 masked: {c2.mask('hue', invert=True)}")
print(f"Result: {result2}")
print(f"Expected: lch(30% 30 375)")
