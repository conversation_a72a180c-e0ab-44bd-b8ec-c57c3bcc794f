:root {
  --playground-edit-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z' /%3E%3C/svg%3E");
  --playground-share-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M14 16V13C10.39 13 7.81 14.43 6 17C6.72 13.33 8.94 9.73 14 9V6L19 11L14 16Z' /%3E%3C/svg%3E");
  --playground-run-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5C2,3.89 2.9,3 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z' /%3E%3C/svg%3E");
  --playground-cancel-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20 6.91L17.09 4L12 9.09L6.91 4L4 6.91L9.09 12L4 17.09L6.91 20L12 14.91L17.09 20L20 17.09L14.91 12L20 6.91Z' /%3E%3C/svg%3E");
}

.playground,
.notebook {
  button:not(.md-clipboard) {
    position: relative;
    margin-top: 0.25em;
    padding: 0.25em 1em;
    padding-left: 2.5em;
    color: var(--md-primary-bg-color);
    font-size: 90%;
    background: var(--md-primary-fg-color);
    border-radius: px2rem(2px);
    opacity: 0.8;

    &:hover {
      cursor: pointer;
      opacity: 1;
    }

    &[disabled] {
      opacity: 0.5;
    }

    &::before {
       position: absolute;
       top: 0.25em;
       left: 1em;
       display: block;
       box-sizing: border-box;
       width: 1.25em;
       height: 1.25em;
       background-color: var(--md-primary-bg-color);
       background-size: 1.25em;
       transition: background-color 125ms;
       mask-repeat: no-repeat;
       mask-size: contain;
       content: "";
    }

    &.playground-edit::before {
      mask-image: var(--playground-edit-icon);
    }

    &.playground-share::before {
      mask-image: var(--playground-share-icon);
    }

    &#__notebook-submit::before,
    &.playground-run::before {
      mask-image: var(--playground-run-icon);
    }

    &#__notebook-cancel::before,
    &.playground-cancel::before {
      mask-image: var(--playground-cancel-icon);
    }

    &.hidden {
      display: none;
    }
  }
}

// Special buttons in our toolbar
.md-typeset .source-link {
  margin-left: px2rem(8px);
}
