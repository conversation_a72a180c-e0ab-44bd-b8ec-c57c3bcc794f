# Contributing &amp; Support

There are many ways to help support this project, regardless of skills and abilities. If you enjoy this project and want
to get involved, consider checking out one of the various ways below. Feel free to get creative, there may be other ways
to contribute in which we have not thought of!

## Become a Sponsor :octicons-heart-fill-16:{: .heart-throb}

Open source projects take time and money. Help support the project by becoming a sponsor. You can add your support at
any tier you feel comfortable with. No amount is too little. We also accept one time contributions via PayPal.

[:octicons-mark-github-16: GitHub Sponsors](https://github.com/sponsors/facelessuser){: .md-button .md-button--primary }
[:fontawesome-brands-paypal: PayPal](https://www.paypal.me/facelessuser){ .md-button}

## Bug Reports

1. Please **read the documentation** and **search the issue tracker** to try and find the answer to your question
  **before** posting an issue.

2. When creating an issue on the repository, please provide as much info as possible:

    - Version being used.
    - Operating system.
    - Version of Python.
    - Errors in console.
    - Detailed description of the problem.
    - Examples for reproducing the error.  You can post pictures, but if specific text or code is required to reproduce
      the issue, please provide the text in a plain text format for easy copy/paste.

    The more info provided, the greater the chance someone will take the time to answer, implement, or fix the issue.

3. Be prepared to answer questions and provide additional information if required.  Issues in which the creator refuses
  to respond to follow up questions will be marked as stale and closed.

## Reviewing Code

Take part in reviewing pull requests and/or reviewing direct commits.  Make suggestions to improve the code and discuss
solutions to overcome weakness in the algorithm.

## Answer Questions in Issues

Take time and answer questions and offer suggestions to people who've created issues in the issue tracker. Often people
will have questions that you might have an answer for.  Or maybe you know how to help them accomplish a specific task
they are asking about. Feel free to share your experience to help others out.

## Pull Requests

Pull requests are welcome, and a great way to help fix bugs and add new features.

## Documentation Improvements

A ton of time has been spent not only creating and supporting this tool and related extensions, but also spent making
this documentation.  If you feel it is still lacking, show your appreciation for the tool and/or extensions by helping
to improve the documentation.
