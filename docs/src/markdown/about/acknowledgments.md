# Acknowledgments

All projects gain help and inspiration from somewhere, and we wanted to document the places in which we we gathered
knowledge, ideas, and help.

## Projects

When we began writing ColorAide, we wanted a simple interface to deal with different colors. We also wanted to support
CSS colors which a lot of people are familiar with. We had a number of questions about the CSS spec and stumbled on
[Color.js][colorjs], a JavaScript library developed and maintained by the co-authors of the CSS spec. We found Color.js
and its authors helped clarify a number of confusing points. Additionally, their library did end up heavily inspiring
many aspects of our own API as its approach very much aligned with the direction we had already started down.

## References

When researching color vision deficiencies, aside from the usual scientific papers, a couple of sites were found to be
quite helpful.

- [daltonlens.org](https://daltonlens.org/) was particularly helpful. As it comes from the perspective of an actual
  Protan, it provided reviews on various algorithm's and explained in great depth some approaches and why they were
  preferred over others.
- [ixora.io](https://ixora.io/projects/colorblindness/color-blindness-simulation-research/) was another useful site
  that went into great details specifically about the Viénot approach.
