[brettel]: https://citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.496.7153&rep=rep1&type=pdf
[colorio]: https://github.com/nschloe/colorio
[colorjs]: https://github.com/LeaVerou/color.js
[color-math]: https://github.com/gtaylor/python-colormath
[colour-science]: https://github.com/colour-science/colour
[compositing-level-1]: https://www.w3.org/TR/compositing-1/
[css-spec-convert]: https://drafts.csswg.org/css-color/#color-conversion-code
[de2000]: http://www.brucelindbloom.com/index.html?Math.html
[de76]: http://www.brucelindbloom.com/index.html?Math.html
[de94]: http://www.brucelindbloom.com/index.html?Math.html
[de99o]: https://de.wikipedia.org/wiki/DIN99-Farbraum
[decmc]: http://www.brucelindbloom.com/index.html?Math.html
[dehyab]: http://markfairchild.org/PDFs/PAP40.pdf
[deitp]: https://kb.portrait.com/help/ictcp-color-difference-metric
[dez]: https://www.osapublishing.org/oe/fulltext.cfm?uri=oe-25-13-15131&id=368272
[extras]: https://github.com/facelessuser/coloraide-extras
[filter-effects]: https://www.w3.org/TR/filter-effects-1/
[floating-point]: https://docs.python.org/3/tutorial/floatingpoint.html#floating-point-arithmetic-issues-and-limitations
[issues]: https://github.com/facelessuser/coloraide/issues
[lch-chroma]: https://colorjs.io/docs/gamut-mapping.html#so-how-does-colorjs-handle-all-this
[machado]: https://www.inf.ufrgs.br/~oliveira/pubs_files/CVD_Simulation/CVD_Simulation.html#Reference
[pillow]: https://pypi.org/project/Pillow/
[porter-duff]: https://www.w3.org/TR/compositing-1/#porterduffcompositingoperators
[source-over]: https://www.w3.org/TR/compositing-1/#porterduffcompositingoperators_srcover
[vienot]: http://vision.psychol.cam.ac.uk/jdmollon/papers/colourmaps.pdf
