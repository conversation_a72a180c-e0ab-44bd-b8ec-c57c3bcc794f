{"version": 3, "file": "extra-notebook-6edfcb69.js", "sources": ["../../../src/js/extra-notebook.js"], "sourcesContent": ["(() => {\n  let pyodide = null\n  let busy = false\n  let raw = \"\"\n  let gist = \"\"\n  let editTemp = {}\n  const reIdNum = /.*?_(\\d+)$/\n  let initialized = false\n  let lastSearch = \"\"\n  let fake = false\n  const tabStart = /^( {1,4}|\\t)/\n  // This is the Python payload that will be executed when the user\n  // presses the `Run` button. It will execute the code, create a\n  // Python console output, find color references, steps, and interpolation\n  // references and render the appropriate preview.\n  const pycode = `\n{{pycode}}\n\nimport micropip\nfrom js import location\n\naction = globals().get('action')\nif action == 'notebook':\n    callback = render_notebook\nelse:\n    callback = render_console\n\nbase = location.pathname.lstrip('/').split('/')[0]\nawait micropip.install([location.origin + '/{}/playground/{}'.format(base, w) for w in wheels])\ncallback()\n`\n\n  const defContent = window.color_notebook.default_playground\n\n  const getContent = content => {\n    return `\n!!! new \"This notebook is powered by [Pyodide](https://github.com/pyodide/pyodide). \\\nLearn more [here](\\\n?notebook=https://gist.githubusercontent.com/facelessuser/7c819668b5eb248ecb9ac608d91391cf/raw/playground.md\\\n). Preview, convert, interpolate, and explore!\"\n\n\\`\\`\\`\\`\\`\\`\\`\\`playground\n${content}\n\\`\\`\\`\\`\\`\\`\\`\\`\n`\n  }\n\n  const fakeDOMContentLoaded = () => {\n    // Send a fake `DOMContentLoaded`\n    fake = true\n    window.document.dispatchEvent(new Event(\"DOMContentLoaded\", {\n      bubbles: true,\n      cancelable: true\n    }))\n  }\n\n  const textResize = inpt => {\n    // Resize inputs based on text height.\n\n    const scrollLeft = window.pageXOffset ||\n      (document.documentElement || document.body.parentNode || document.body).scrollLeft\n\n    const scrollTop  = window.pageYOffset ||\n      (document.documentElement || document.body.parentNode || document.body).scrollTop\n\n    inpt.style.height = \"5px\"\n    inpt.style.height = `${inpt.scrollHeight}px`\n\n    window.scrollTo(scrollLeft, scrollTop)\n  }\n\n  const encodeuri = uri => {\n    // Encode the URI component.\n\n    return encodeURIComponent(uri).replace(/[.!'()*]/g, c => {\n      return `%${c.charCodeAt(0).toString(16)}`\n    })\n  }\n\n  const pyexecute = async currentID => {\n    // Execute Python code inside a playground\n\n    const currentInputs = document.getElementById(`__playground-inputs_${currentID}`)\n    currentInputs.setAttribute(\"readonly\", \"\")\n    pyodide.globals.set(\"id_num\", currentID)\n    pyodide.globals.set(\"action\", \"playground\")\n    pyodide.globals.set('wheels', window.color_notebook.playground_wheels)\n    await pyodide.runPythonAsync(pycode)\n    currentInputs.removeAttribute(\"readonly\")\n  }\n\n  const pyrender = async text => {\n    // Render an entire notebook page\n\n    pyodide.globals.set(\"content\", text)\n    pyodide.globals.set(\"action\", \"notebook\")\n    pyodide.globals.set('wheels', window.color_notebook.notebook_wheels)\n    await pyodide.runPythonAsync(pycode)\n    const src = document.getElementById(\"__notebook-input\")\n    if (src) {\n      raw = text\n      src.value = text\n    }\n    if (window.location.hash) {\n      // Force jumping to hashes\n      window.location.href = window.location.href // eslint-disable-line no-self-assign\n    }\n  }\n\n  const setupPyodide = async() => {\n    // Load `Pyodide` and the any default packages we can need and can load.\n\n    if (!initialized) {\n      initialized = true\n      pyodide = await loadPyodide({ // eslint-disable-line no-undef\n        indexURL: \"https://cdn.jsdelivr.net/pyodide/v0.20.0/full/\",\n        fullStdLib: false\n      })\n      await pyodide.loadPackage([\"micropip\"])\n    }\n  }\n\n  const showBusy = (target, label, relative) => {\n    // Show busy indicator\n\n    const loaderLabel = (typeof label === \"undefined\" || label === null) ? \"Loading...\" : label\n    const classes = relative ? \"loading relative\" : \"loading\"\n    const template = document.createElement(\"template\")\n    template.innerHTML = `<div class=\"${classes}\"><div class=\"loader\"></div><div>${loaderLabel}</div></div>`\n    target.appendChild(template.content.firstChild)\n  }\n\n  const hideBusy = target => {\n    // Hide busy indicator\n\n    const loading = target.querySelector(\".loading\")\n    if (loading) {\n      target.removeChild(target.querySelector(\".loading\"))\n    }\n  }\n\n  const popState = () => {\n    // Handle notebook history\n\n    const base = window.location.pathname.split('/')[1]\n    if (\n      window.location.pathname === `/${base}/playground/`\n    ) {\n      const current = decodeURIComponent(new URLSearchParams(window.location.search).toString())\n      if (current !== lastSearch) {\n        main(false) // eslint-disable-line no-use-before-define\n      }\n    }\n  }\n\n  const interceptClickEvent = e => {\n    // Catch links to other notebook pages and handle them\n\n    const base = window.location.pathname.split('/')[1]\n    const target = e.target || e.srcElement\n    if (target.tagName === \"A\" && main) { // eslint-disable-line no-use-before-define\n      if (\n        target.getAttribute(\"href\") &&\n        target.host === window.location.host &&\n        window.location.pathname === `/${base}/playground/\"` &&\n        window.location.pathname === target.pathname &&\n        window.location.search !== target.search\n      ) {\n        e.preventDefault()\n        const search = new URLSearchParams(target.search)\n        const state = {}\n        for (const [key, value] of search) {\n          state[key] = value\n        }\n        history.pushState(state, \"\", target.href)\n        main(false) // eslint-disable-line no-use-before-define\n      }\n    }\n  }\n\n  const handleTab = e => {\n    // Prevent tab from tabbing out.\n\n    if (e.key === 'Tab') {\n      const target = e.target\n\n      if (target.selectionStart !== target.selectionEnd) {\n        e.preventDefault()\n\n        let start = target.selectionStart\n        let end = target.selectionEnd\n\n        const text = target.value\n\n        while (start > 0 && text[start - 1] !== '\\n') {\n          start--\n        }\n        while (end > 0 && text[end - 1] !== '\\n' && end < text.length) {\n          end++\n        }\n\n        let lines = text.substr(start, end - start).split('\\n')\n\n        for (let i = 0; i < lines.length; i++) {\n\n          // Don't indent last line if cursor at start of line\n          if (i === lines.length - 1 && lines[i].length === 0) {\n            continue\n          }\n\n          // Indent or deindent\n          if (e.shiftKey) {\n            lines[i] = lines[i].replace(tabStart, '')\n          } else {\n            lines[i] = `    ${lines[i]}`\n          }\n        }\n        lines = lines.join('\\n')\n\n        // Update the text area\n        target.value = text.substr(0, start) + lines + text.substr(end)\n        target.selectionStart = start\n        target.selectionEnd = start + lines.length\n      }\n    }\n  }\n\n  const init = async first => {\n    // Setup input highlighting and events to run Python code blocks.\n\n    const notebook = document.getElementById(\"__notebook-source\")\n    const playgrounds = document.querySelectorAll(\".playground\")\n    playgrounds.forEach(pg => {\n\n      const currentID = pg.id.replace(reIdNum, \"$1\")\n      const inputs = document.getElementById(`__playground-inputs_${currentID}`)\n      const results = document.getElementById(`__playground-results_${currentID}`)\n      const pgcode = document.getElementById(`__playground-code_${currentID}`)\n      const buttonEdit = document.querySelector(`button#__playground-edit_${currentID}`)\n      const buttonShare = document.querySelector(`button#__playground-share_${currentID}`)\n      const buttonRun = document.querySelector(`button#__playground-run_${currentID}`)\n      const buttonCancel = document.querySelector(`button#__playground-cancel_${currentID}`)\n\n      inputs.addEventListener(\"input\", () => {\n        // Adjust textarea height on text input.\n\n        textResize(inputs)\n      })\n\n      inputs.addEventListener('keydown', handleTab)\n\n      if (notebook && first) {\n        const notebookInput = document.getElementById(\"__notebook-input\")\n\n        notebookInput.addEventListener(\"input\", e => {\n          // Adjust textarea height on text input.\n\n          textResize(e.target)\n        })\n\n        notebookInput.addEventListener('keydown', handleTab)\n\n        const editPage = document.getElementById(\"__notebook-edit\")\n        editPage.addEventListener(\"click\", () => {\n          editTemp[notebookInput.id] = notebookInput.value\n          document.getElementById(\"__notebook-render\").classList.toggle(\"hidden\")\n          document.getElementById(\"__notebook-source\").classList.toggle(\"hidden\")\n          textResize(document.getElementById(\"__notebook-input\"))\n        })\n\n        document.getElementById(\"__notebook-md-gist\").addEventListener(\"click\", async e => {\n          let uri = prompt(\"Please enter link to the Markdown page source:\", gist) // eslint-disable-line no-alert\n          if (uri !== null) {\n            uri = encodeuri(uri)\n            e.preventDefault()\n            history.pushState({notebook: uri}, \"\", `?${new URLSearchParams(`notebook=${uri}`).toString()}`)\n            main(false) // eslint-disable-line no-use-before-define\n          }\n        })\n\n        document.getElementById(\"__notebook-py-gist\").addEventListener(\"click\", async e => {\n          let uri = prompt(\"Please enter the link to the Python code source:\", gist) // eslint-disable-line no-alert\n          if (uri !== null) {\n            uri = encodeuri(uri)\n            e.preventDefault()\n            history.pushState({source: uri}, \"\", `?${new URLSearchParams(`source=${uri}`).toString()}`)\n            main(false) // eslint-disable-line no-use-before-define\n          }\n        })\n\n        document.getElementById(\"__notebook-input\").value = raw\n        document.getElementById(\"__notebook-cancel\").addEventListener(\"click\", () => {\n          notebookInput.value = editTemp[notebookInput.id]\n          delete editTemp[notebookInput.id]\n          document.getElementById(\"__notebook-render\").classList.toggle(\"hidden\")\n          document.getElementById(\"__notebook-source\").classList.toggle(\"hidden\")\n        })\n\n        document.getElementById(\"__notebook-submit\").addEventListener(\"click\", async() => {\n          const render = document.getElementById(\"__notebook-render\")\n          raw = document.getElementById(\"__notebook-input\").value\n          render.classList.toggle(\"hidden\")\n          document.getElementById(\"__notebook-source\").classList.toggle(\"hidden\")\n          const article = document.querySelector(\"article\")\n          showBusy(article, \"Loading Notebook...\")\n          render.innerHTML = \"\"\n          editTemp = {}\n          await setupPyodide()\n          await pyrender(raw)\n          await init()\n          hideBusy(article)\n        })\n      }\n\n      inputs.addEventListener(\"touchmove\", e => {\n        // Stop propagation on \"touchmove\".\n\n        e.stopPropagation()\n      })\n\n      results.addEventListener(\"click\", e => {\n        // Handle clicks on results and copies color from single color swatch when clicked.\n\n        const el = e.target\n        if (el.matches('span.swatch-color')) {\n          let content = ''\n          const parent = el.parentNode\n          if (!parent.matches('span.swatch-gradient')) {\n            content = parent.getAttribute('title').replace('Copy to clipboard', '')\n            content = content.replace('\\n', '')\n            if (window.clipboardData && window.clipboardData.setData) {\n              // Old `IE`` handling, do we really need this?\n              return window.clipboardData.setData(\"Text\", content)\n            } else if (document.queryCommandSupported && document.queryCommandSupported(\"copy\")) {\n              const textarea = document.createElement(\"textarea\")\n              textarea.textContent = content\n              textarea.style.position = \"fixed\"\n              document.body.appendChild(textarea)\n              textarea.select()\n              try {\n                return document.execCommand(\"copy\")\n              } catch (ex) {\n                return prompt(\"Copy to clipboard: Ctrl+C, Enter\", content) // eslint-disable-line no-alert\n              } finally {\n                document.body.removeChild(textarea)\n              }\n            }\n          }\n        }\n      })\n\n      buttonEdit.addEventListener(\"click\", async() => {\n        // Handle the button click: show source or execute source.\n\n        editTemp[currentID] = inputs.value\n        pgcode.classList.toggle(\"hidden\")\n        results.classList.toggle(\"hidden\")\n        buttonRun.classList.toggle(\"hidden\")\n        buttonCancel.classList.toggle(\"hidden\")\n        buttonEdit.classList.toggle(\"hidden\")\n        buttonShare.classList.toggle(\"hidden\")\n        textResize(inputs)\n        inputs.focus()\n      })\n\n      buttonShare.addEventListener(\"click\", async() => {\n        // Handle the share click: copy URL with code as parameter.\n\n        const base = window.location.pathname.split('/')[1]\n        const uri = encodeuri(inputs.value)\n        const loc = window.location\n        let pathname = \"/playground/\"\n        if (loc.pathname.startsWith(`/${base}/`)) {\n          pathname = `/${base}/playground/`\n        }\n        const path = `${loc.protocol}//${loc.host}${pathname}?code=${uri}`\n        if (uri.length > 1000) {\n          alert(\"Code must be under a 1000 characters to generate a URL!\") // eslint-disable-line no-alert\n        } else {\n          navigator.clipboard.writeText(path).then(async() => {\n            alert(\"Link copied to clipboard :)\") // eslint-disable-line no-alert\n          }, async() => {\n            alert(\"Failed to copy link clipboard!\") // eslint-disable-line no-alert\n          })\n        }\n      })\n\n      buttonRun.addEventListener(\"click\", async() => {\n        // Handle the button click: show source or execute source.\n\n        if (busy) {\n          return\n        }\n\n        busy = true\n        // Load Pyodide and related packages.\n        const form = pgcode.querySelector(\"form\")\n        showBusy(form, null, true)\n        const buttons = document.querySelectorAll(\".playground .playground-run\")\n        if (buttons) {\n          buttons.forEach(b => {\n            b.setAttribute(\"disabled\", \"\")\n          })\n        }\n        await setupPyodide()\n        results.querySelector(\"code\").innerHTML = \"\"\n        await pyexecute(currentID)\n        if (buttons) {\n          buttons.forEach(b => {\n            b.removeAttribute(\"disabled\")\n          })\n        }\n        hideBusy(form)\n        pgcode.classList.toggle(\"hidden\")\n        results.classList.toggle(\"hidden\")\n        buttonEdit.classList.toggle(\"hidden\")\n        buttonShare.classList.toggle(\"hidden\")\n        buttonRun.classList.toggle(\"hidden\")\n        buttonCancel.classList.toggle(\"hidden\")\n\n        delete editTemp[currentID]\n        busy = false\n      })\n\n      buttonCancel.addEventListener(\"click\", () => {\n        // Cancel edit.\n\n        inputs.value = editTemp[currentID]\n        delete editTemp[currentID]\n        pgcode.classList.toggle(\"hidden\")\n        results.classList.toggle(\"hidden\")\n        buttonEdit.classList.toggle(\"hidden\")\n        buttonShare.classList.toggle(\"hidden\")\n        buttonRun.classList.toggle(\"hidden\")\n        buttonCancel.classList.toggle(\"hidden\")\n      })\n    })\n  }\n\n  const main = async first => {\n    // Load external source to render in a playground.\n    // This can be something like a file on a gist we must read in (?source=)\n    // or raw code (?code=).\n\n    editTemp = {}\n\n    if (window.location.pathname.endsWith(\"/playground/\")) {\n      const params = new URLSearchParams(window.location.search)\n      const loadMsg = \"Loading Pyodide...\"\n      const pageMsg = \"Loading Notebook...\"\n      const uri = params.has(\"source\") ? params.get(\"source\") : params.get(\"notebook\")\n      const article = document.querySelector(\"article\")\n      if (uri !== null && uri.trim()) {\n        // A source was specified, so load it.\n        showBusy(article, loadMsg)\n        await setupPyodide()\n        hideBusy(article)\n        showBusy(article, pageMsg)\n        try {\n          const gistType = params.has(\"source\") ? \"source\" : \"notebook\"\n          lastSearch = decodeURIComponent(params.toString())\n          let value = \"\"\n          const xhr = new XMLHttpRequest()\n          gist = uri\n          xhr.open(\"GET\", uri, true)\n          xhr.onload = async() => {\n            // Try and load the requested content\n            if (xhr.readyState === 4) {\n              if (xhr.status === 200) {\n                value = xhr.responseText\n              }\n            }\n\n            if (gistType === \"source\") {\n              value = getContent(value)\n            }\n            await pyrender(value)\n            await init(first)\n            hideBusy(article)\n            fakeDOMContentLoaded()\n          }\n          xhr.send()\n        } catch (err) {} // eslint-disable-line no-empty\n      } else {\n        gist = \"\"\n        const content = getContent(params.has(\"code\") ? params.get(\"code\") : defContent)\n        lastSearch = decodeURIComponent(params.toString())\n        showBusy(article, loadMsg)\n        await setupPyodide()\n        hideBusy(article)\n        showBusy(article, pageMsg)\n        await pyrender(content)\n        await init(first)\n        hideBusy(article)\n        fakeDOMContentLoaded()\n      }\n    } else {\n      gist = \"\"\n      lastSearch = \"\"\n      init(first)\n    }\n  }\n\n  // Capture links in notebook pages so that we can make playgound links load instantly\n  document.addEventListener(\"click\", interceptClickEvent)\n\n  // Handle history of notebook pages as they are loaded dynamically\n  window.addEventListener(\"popstate\", popState)\n\n  // Before leaving, turn off fake, just in case we navigated away before finished\n  window.addEventListener(\"unload\", () => {\n    fake = true\n  })\n\n  // Attach main via subscribe (subscribes to Materials on page load and instant page loads)\n  window.document$.subscribe(() => {\n    // To get other libraries to to reload, we may create a fake `DOMContentLoaded`\n    // No need to process these events.\n    if (fake) {\n      fake = false\n      return\n    }\n    main(true)\n  })\n})()\n"], "names": ["pyodide", "busy", "raw", "gist", "editTemp", "reIdNum", "initialized", "lastSearch", "fake", "tabStart", "pycode", "defContent", "get<PERSON>ontent", "fakeDOMContentLoaded", "textResize", "encodeuri", "pyexecute", "pyre<PERSON>", "setupPyodide", "showBusy", "hideBusy", "handleTab", "init", "main", "window", "color_notebook", "default_playground", "content", "concat", "document", "dispatchEvent", "Event", "bubbles", "cancelable", "inpt", "scrollLeft", "pageXOffset", "documentElement", "body", "parentNode", "scrollTop", "pageYOffset", "style", "height", "scrollHeight", "scrollTo", "uri", "encodeURIComponent", "replace", "c", "charCodeAt", "toString", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "currentID", "currentInputs", "wrap", "_context", "prev", "next", "getElementById", "setAttribute", "globals", "set", "playground_wheels", "runPythonAsync", "removeAttribute", "stop", "_callee", "_x", "apply", "this", "arguments", "_ref2", "text", "src", "_context2", "notebook_wheels", "value", "location", "hash", "href", "_callee2", "_x2", "_ref3", "_callee3", "_context3", "loadPyodide", "indexURL", "fullStdLib", "sent", "loadPackage", "target", "label", "relative", "loaderLabel", "classes", "template", "createElement", "innerHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "e", "key", "selectionStart", "selectionEnd", "preventDefault", "start", "end", "length", "lines", "substr", "split", "i", "shift<PERSON>ey", "join", "_ref4", "first", "notebook", "_context12", "querySelectorAll", "for<PERSON>ach", "pg", "id", "inputs", "results", "pgcode", "buttonEdit", "buttonShare", "buttonRun", "buttonCancel", "addEventListener", "notebookInput", "classList", "toggle", "_ref5", "_context4", "prompt", "history", "pushState", "URLSearchParams", "_callee4", "_x4", "_ref6", "_context5", "source", "_callee5", "_x5", "_callee6", "render", "article", "_context6", "stopPropagation", "el", "matches", "parent", "getAttribute", "clipboardData", "setData", "queryCommandSupported", "textarea", "textContent", "position", "select", "execCommand", "ex", "_callee7", "_context7", "focus", "_callee10", "base", "loc", "pathname", "path", "_context10", "startsWith", "protocol", "host", "alert", "navigator", "clipboard", "writeText", "then", "_callee8", "_context8", "_callee9", "_context9", "_callee11", "form", "buttons", "_context11", "abrupt", "b", "_callee12", "_x3", "_ref13", "params", "loadMsg", "pageMsg", "gistType", "xhr", "_context14", "endsWith", "search", "has", "get", "trim", "decodeURIComponent", "XMLHttpRequest", "open", "onload", "_callee13", "_context13", "readyState", "status", "responseText", "send", "err", "_callee14", "_x6", "srcElement", "tagName", "_step", "state", "_iterator", "_createForOfIteratorHelper", "s", "n", "done", "_step$value", "_slicedToArray", "f", "document$", "subscribe"], "mappings": ";;giPAAA,IACMA,EACAC,EACAC,EACAC,EACAC,EACEC,EACFC,EACAC,EACAC,EACEC,EAKAC,EAiBAC,EAEAC,EAaAC,EASAC,EAeAC,EAQAC,EAYAC,EAkBAC,EAaAC,EAUAC,EAgDAC,EA+CAC,EAoNAC,EAtbFvB,EAAU,KACVC,GAAO,EACPC,EAAM,GACNC,EAAO,GACPC,EAAW,GACTC,EAAU,aACZC,GAAc,EACdC,EAAa,GACbC,GAAO,EACLC,EAAW,eAKXC,EAAN,65oBAiBMC,EAAaa,OAAOC,eAAeC,mBAEnCd,EAAa,SAAAe,GACjB,MAAA,8RAAAC,OAOFD,EAPE,iBAYId,EAAuB,WAE3BL,GAAO,EACPgB,OAAOK,SAASC,cAAc,IAAIC,MAAM,mBAAoB,CAC1DC,SAAS,EACTC,YAAY,MAIVnB,EAAa,SAAAoB,GAGjB,IAAMC,EAAaX,OAAOY,cACvBP,SAASQ,iBAAmBR,SAASS,KAAKC,YAAcV,SAASS,MAAMH,WAEpEK,EAAahB,OAAOiB,cACvBZ,SAASQ,iBAAmBR,SAASS,KAAKC,YAAcV,SAASS,MAAME,UAE1EN,EAAKQ,MAAMC,OAAS,MACpBT,EAAKQ,MAAMC,OAAYT,GAAAA,OAAAA,EAAKU,aAA5B,MAEApB,OAAOqB,SAASV,EAAYK,IAGxBzB,EAAY,SAAA+B,GAGhB,OAAOC,mBAAmBD,GAAKE,QAAQ,aAAa,SAAAC,GAClD,MAAWA,IAAAA,OAAAA,EAAEC,WAAW,GAAGC,SAAS,SAIlCnC,EAAS,WAAA,IAAAoC,EAAAC,EAAAC,IAAAC,MAAG,WAAMC,GAAN,IAAAC,EAAA,OAAAH,IAAAI,MAAA,SAAAC,GAAA,OAAA,OAAAA,EAAAC,KAAAD,EAAAE,MAAA,KAAA,EAAA,OAGVJ,EAAgB5B,SAASiC,eAAT,uBAAAlC,OAA+C4B,KACvDO,aAAa,WAAY,IACvC/D,EAAQgE,QAAQC,IAAI,SAAUT,GAC9BxD,EAAQgE,QAAQC,IAAI,SAAU,cAC9BjE,EAAQgE,QAAQC,IAAI,SAAUzC,OAAOC,eAAeyC,mBAPpCP,EAAAE,KAAA,EAQV7D,EAAQmE,eAAezD,GARb,KAAA,EAShB+C,EAAcW,gBAAgB,YATd,KAAA,EAAA,IAAA,MAAA,OAAAT,EAAAU,UAAAC,OAAH,OAAA,SAAAC,GAAA,OAAAnB,EAAAoB,MAAAC,KAAAC,YAAA,GAYTzD,EAAQ,WAAA,IAAA0D,EAAAtB,EAAAC,IAAAC,MAAG,WAAMqB,GAAN,IAAAC,EAAA,OAAAvB,IAAAI,MAAA,SAAAoB,GAAA,OAAA,OAAAA,EAAAlB,KAAAkB,EAAAjB,MAAA,KAAA,EAAA,OAGf7D,EAAQgE,QAAQC,IAAI,UAAWW,GAC/B5E,EAAQgE,QAAQC,IAAI,SAAU,YAC9BjE,EAAQgE,QAAQC,IAAI,SAAUzC,OAAOC,eAAesD,iBALrCD,EAAAjB,KAAA,EAMT7D,EAAQmE,eAAezD,GANd,KAAA,GAOTmE,EAAMhD,SAASiC,eAAe,uBAElC5D,EAAM0E,EACNC,EAAIG,MAAQJ,GAEVpD,OAAOyD,SAASC,OAElB1D,OAAOyD,SAASE,KAAO3D,OAAOyD,SAASE,MAd1B,KAAA,EAAA,IAAA,MAAA,OAAAL,EAAAT,UAAAe,OAAH,OAAA,SAAAC,GAAA,OAAAV,EAAAH,MAAAC,KAAAC,YAAA,GAkBRxD,EAAY,WAAA,IAAGoE,EAAAjC,EAAAC,IAAAC,MAAA,SAAAgC,IAAA,OAAAjC,IAAAI,MAAA,SAAA8B,GAAA,OAAA,OAAAA,EAAA5B,KAAA4B,EAAA3B,MAAA,KAAA,EAAA,GAGdvD,EAHc,CAAAkF,EAAA3B,KAAA,EAAA,MAAA,OAIjBvD,GAAc,EAJGkF,EAAA3B,KAAA,EAKD4B,YAAY,CAC1BC,SAAU,iDACVC,YAAY,IAPG,KAAA,EAAA,OAKjB3F,EALiBwF,EAAAI,KAAAJ,EAAA3B,KAAA,EASX7D,EAAQ6F,YAAY,CAAC,aATV,KAAA,EAAA,IAAA,MAAA,OAAAL,EAAAnB,UAAAkB,OAAH,OAAA,WAAA,OAAAD,EAAAd,MAAAC,KAAAC,YAAA,GAaZvD,EAAW,SAAC2E,EAAQC,EAAOC,GAG/B,IAAMC,EAAe,MAAOF,EAA2C,aAAeA,EAChFG,EAAUF,EAAW,mBAAqB,UAC1CG,EAAWtE,SAASuE,cAAc,YACxCD,EAASE,UAA2BH,eAAAA,OAAAA,8CAA2CD,EAA/E,gBACAH,EAAOQ,YAAYH,EAASxE,QAAQ4E,aAGhCnF,EAAW,SAAA0E,GAGCA,EAAOU,cAAc,aAEnCV,EAAOW,YAAYX,EAAOU,cAAc,cA2CtCnF,EAAY,SAAAqF,GAGhB,GAAc,QAAVA,EAAEC,IAAe,CACnB,IAAMb,EAASY,EAAEZ,OAEjB,GAAIA,EAAOc,iBAAmBd,EAAOe,aAAc,CACjDH,EAAEI,iBAOF,IALA,IAAIC,EAAQjB,EAAOc,eACfI,EAAMlB,EAAOe,aAEXjC,EAAOkB,EAAOd,MAEb+B,EAAQ,GAAyB,OAApBnC,EAAKmC,EAAQ,IAC/BA,IAEF,KAAOC,EAAM,GAAuB,OAAlBpC,EAAKoC,EAAM,IAAeA,EAAMpC,EAAKqC,QACrDD,IAKF,IAFA,IAAIE,EAAQtC,EAAKuC,OAAOJ,EAAOC,EAAMD,GAAOK,MAAM,MAEzCC,EAAI,EAAGA,EAAIH,EAAMD,OAAQI,IAG5BA,IAAMH,EAAMD,OAAS,GAAyB,IAApBC,EAAMG,GAAGJ,SAKnCP,EAAEY,SACJJ,EAAMG,GAAKH,EAAMG,GAAGrE,QAAQvC,EAAU,IAEtCyG,EAAMG,iBAAYH,EAAMG,KAG5BH,EAAQA,EAAMK,KAAK,MAGnBzB,EAAOd,MAAQJ,EAAKuC,OAAO,EAAGJ,GAASG,EAAQtC,EAAKuC,OAAOH,GAC3DlB,EAAOc,eAAiBG,EACxBjB,EAAOe,aAAeE,EAAQG,EAAMD,UAKpC3F,EAAI,WAAA,IAAAkG,EAAAnE,EAAAC,IAAAC,MAAG,WAAMkE,GAAN,IAAAC,EAAA,OAAApE,IAAAI,MAAA,SAAAiE,GAAA,OAAA,OAAAA,EAAA/D,KAAA+D,EAAA9D,MAAA,KAAA,EAGL6D,EAAW7F,SAASiC,eAAe,qBACrBjC,SAAS+F,iBAAiB,eAClCC,SAAQ,SAAAC,GAElB,IAAMtE,EAAYsE,EAAGC,GAAG/E,QAAQ3C,EAAS,MACnC2H,EAASnG,SAASiC,eAAT,uBAAAlC,OAA+C4B,IACxDyE,EAAUpG,SAASiC,eAAT,wBAAAlC,OAAgD4B,IAC1D0E,EAASrG,SAASiC,eAAT,qBAAAlC,OAA6C4B,IACtD2E,EAAatG,SAAS2E,cAAT,4BAAA5E,OAAmD4B,IAChE4E,EAAcvG,SAAS2E,cAAT,6BAAA5E,OAAoD4B,IAClE6E,EAAYxG,SAAS2E,cAAT,2BAAA5E,OAAkD4B,IAC9D8E,EAAezG,SAAS2E,cAAT,8BAAA5E,OAAqD4B,IAU1E,GARAwE,EAAOO,iBAAiB,SAAS,WAG/BzH,EAAWkH,MAGbA,EAAOO,iBAAiB,UAAWlH,GAE/BqG,GAAYD,EAAO,CACrB,IAAMe,EAAgB3G,SAASiC,eAAe,oBAE9C0E,EAAcD,iBAAiB,SAAS,SAAA7B,GAGtC5F,EAAW4F,EAAEZ,WAGf0C,EAAcD,iBAAiB,UAAWlH,GAEzBQ,SAASiC,eAAe,mBAChCyE,iBAAiB,SAAS,WACjCnI,EAASoI,EAAcT,IAAMS,EAAcxD,MAC3CnD,SAASiC,eAAe,qBAAqB2E,UAAUC,OAAO,UAC9D7G,SAASiC,eAAe,qBAAqB2E,UAAUC,OAAO,UAC9D5H,EAAWe,SAASiC,eAAe,wBAGrCjC,SAASiC,eAAe,sBAAsByE,iBAAiB,QAA/D,WAAA,IAAAI,EAAAtF,EAAAC,IAAAC,MAAwE,WAAMmD,GAAN,IAAA5D,EAAA,OAAAQ,IAAAI,MAAA,SAAAkF,GAAA,OAAA,OAAAA,EAAAhF,KAAAgF,EAAA/E,MAAA,KAAA,EAE1D,QADRf,EAAM+F,OAAO,iDAAkD1I,MAEjE2C,EAAM/B,EAAU+B,GAChB4D,EAAEI,iBACFgC,QAAQC,UAAU,CAACrB,SAAU5E,GAAM,GAAQ,IAAAlB,OAAA,IAAIoH,gBAA4BlG,YAAAA,OAAAA,IAAOK,aAClF5B,GAAK,IAN+D,KAAA,EAAA,IAAA,MAAA,OAAAqH,EAAAvE,UAAA4E,OAAxE,OAAA,SAAAC,GAAA,OAAAP,EAAAnE,MAAAC,KAAAC,YAAA,IAUA7C,SAASiC,eAAe,sBAAsByE,iBAAiB,QAA/D,WAAA,IAAAY,EAAA9F,EAAAC,IAAAC,MAAwE,WAAMmD,GAAN,IAAA5D,EAAA,OAAAQ,IAAAI,MAAA,SAAA0F,GAAA,OAAA,OAAAA,EAAAxF,KAAAwF,EAAAvF,MAAA,KAAA,EAE1D,QADRf,EAAM+F,OAAO,mDAAoD1I,MAEnE2C,EAAM/B,EAAU+B,GAChB4D,EAAEI,iBACFgC,QAAQC,UAAU,CAACM,OAAQvG,GAAM,GAAQ,IAAAlB,OAAA,IAAIoH,gBAA0BlG,UAAAA,OAAAA,IAAOK,aAC9E5B,GAAK,IAN+D,KAAA,EAAA,IAAA,MAAA,OAAA6H,EAAA/E,UAAAiF,OAAxE,OAAA,SAAAC,GAAA,OAAAJ,EAAA3E,MAAAC,KAAAC,YAAA,IAUA7C,SAASiC,eAAe,oBAAoBkB,MAAQ9E,EACpD2B,SAASiC,eAAe,qBAAqByE,iBAAiB,SAAS,WACrEC,EAAcxD,MAAQ5E,EAASoI,EAAcT,WACtC3H,EAASoI,EAAcT,IAC9BlG,SAASiC,eAAe,qBAAqB2E,UAAUC,OAAO,UAC9D7G,SAASiC,eAAe,qBAAqB2E,UAAUC,OAAO,aAGhE7G,SAASiC,eAAe,qBAAqByE,iBAAiB,QAASlF,EAAAC,IAAAC,MAAA,SAAAiG,IAAA,IAAAC,EAAAC,EAAA,OAAApG,IAAAI,MAAA,SAAAiG,GAAA,OAAA,OAAAA,EAAA/F,KAAA+F,EAAA9F,MAAA,KAAA,EAAA,OAC/D4F,EAAS5H,SAASiC,eAAe,qBACvC5D,EAAM2B,SAASiC,eAAe,oBAAoBkB,MAClDyE,EAAOhB,UAAUC,OAAO,UACxB7G,SAASiC,eAAe,qBAAqB2E,UAAUC,OAAO,UACxDgB,EAAU7H,SAAS2E,cAAc,WACvCrF,EAASuI,EAAS,uBAClBD,EAAOpD,UAAY,GACnBjG,EAAW,GAR0DuJ,EAAA9F,KAAA,GAS/D3C,IAT+D,KAAA,GAAA,OAAAyI,EAAA9F,KAAA,GAU/D5C,EAASf,GAVsD,KAAA,GAAA,OAAAyJ,EAAA9F,KAAA,GAW/DvC,IAX+D,KAAA,GAYrEF,EAASsI,GAZ4D,KAAA,GAAA,IAAA,MAAA,OAAAC,EAAAtF,UAAAmF,QAgBzExB,EAAOO,iBAAiB,aAAa,SAAA7B,GAGnCA,EAAEkD,qBAGJ3B,EAAQM,iBAAiB,SAAS,SAAA7B,GAGhC,IAAMmD,EAAKnD,EAAEZ,OACb,GAAI+D,EAAGC,QAAQ,qBAAsB,CACnC,IAAInI,EAAU,GACRoI,EAASF,EAAGtH,WAClB,IAAKwH,EAAOD,QAAQ,wBAAyB,CAG3C,GADAnI,GADAA,EAAUoI,EAAOC,aAAa,SAAShH,QAAQ,oBAAqB,KAClDA,QAAQ,KAAM,IAC5BxB,OAAOyI,eAAiBzI,OAAOyI,cAAcC,QAE/C,OAAO1I,OAAOyI,cAAcC,QAAQ,OAAQvI,GACvC,GAAIE,SAASsI,uBAAyBtI,SAASsI,sBAAsB,QAAS,CACnF,IAAMC,EAAWvI,SAASuE,cAAc,YACxCgE,EAASC,YAAc1I,EACvByI,EAAS1H,MAAM4H,SAAW,QAC1BzI,SAASS,KAAKgE,YAAY8D,GAC1BA,EAASG,SACT,IACE,OAAO1I,SAAS2I,YAAY,QAC5B,MAAOC,GACP,OAAO5B,OAAO,mCAAoClH,GAC1C,QACRE,SAASS,KAAKmE,YAAY2D,UAOpCjC,EAAWI,iBAAiB,QAASlF,EAAAC,IAAAC,MAAA,SAAAmH,IAAA,OAAApH,IAAAI,MAAA,SAAAiH,GAAA,OAAA,OAAAA,EAAA/G,KAAA+G,EAAA9G,MAAA,KAAA,EAGnCzD,EAASoD,GAAawE,EAAOhD,MAC7BkD,EAAOO,UAAUC,OAAO,UACxBT,EAAQQ,UAAUC,OAAO,UACzBL,EAAUI,UAAUC,OAAO,UAC3BJ,EAAaG,UAAUC,OAAO,UAC9BP,EAAWM,UAAUC,OAAO,UAC5BN,EAAYK,UAAUC,OAAO,UAC7B5H,EAAWkH,GACXA,EAAO4C,QAX4B,KAAA,EAAA,IAAA,MAAA,OAAAD,EAAAtG,UAAAqG,QAcrCtC,EAAYG,iBAAiB,QAASlF,EAAAC,IAAAC,MAAA,SAAAsH,IAAA,IAAAC,EAAAhI,EAAAiI,EAAAC,EAAAC,EAAA,OAAA3H,IAAAI,MAAA,SAAAwH,GAAA,OAAA,OAAAA,EAAAtH,KAAAsH,EAAArH,MAAA,KAAA,EAG9BiH,EAAOtJ,OAAOyD,SAAS+F,SAAS5D,MAAM,KAAK,GAC3CtE,EAAM/B,EAAUiH,EAAOhD,OACvB+F,EAAMvJ,OAAOyD,SACf+F,EAAW,eACXD,EAAIC,SAASG,WAAeL,IAAAA,OAAAA,UAC9BE,EAAQ,IAAApJ,OAAOkJ,EAAf,iBAEIG,EAV8B,GAAArJ,OAUpBmJ,EAAIK,SAVgB,MAAAxJ,OAUHmJ,EAAIM,MAVDzJ,OAUQoJ,EAVR,UAAApJ,OAUyBkB,GACzDA,EAAImE,OAAS,IACfqE,MAAM,2DAENC,UAAUC,UAAUC,UAAUR,GAAMS,KAAKrI,EAAAC,IAAAC,MAAA,SAAAoI,IAAA,OAAArI,IAAAI,MAAA,SAAAkI,GAAA,OAAA,OAAAA,EAAAhI,KAAAgI,EAAA/H,MAAA,KAAA,EACvCyH,MAAM,+BADiC,KAAA,EAAA,IAAA,MAAA,OAAAM,EAAAvH,UAAAsH,OAEtCtI,EAAAC,IAAAC,MAAA,SAAAsI,IAAA,OAAAvI,IAAAI,MAAA,SAAAoI,GAAA,OAAA,OAAAA,EAAAlI,KAAAkI,EAAAjI,MAAA,KAAA,EACDyH,MAAM,kCADL,KAAA,EAAA,IAAA,MAAA,OAAAQ,EAAAzH,UAAAwH,QAhB+B,KAAA,EAAA,IAAA,MAAA,OAAAX,EAAA7G,UAAAwG,QAsBtCxC,EAAUE,iBAAiB,QAASlF,EAAAC,IAAAC,MAAA,SAAAwI,IAAA,IAAAC,EAAAC,EAAA,OAAA3I,IAAAI,MAAA,SAAAwI,GAAA,OAAA,OAAAA,EAAAtI,KAAAsI,EAAArI,MAAA,KAAA,EAAA,IAG9B5D,EAH8B,CAAAiM,EAAArI,KAAA,EAAA,MAAA,OAAAqI,EAAAC,OAAA,UAAA,KAAA,EAAA,OAOlClM,GAAO,EAED+L,EAAO9D,EAAO1B,cAAc,QAClCrF,EAAS6K,EAAM,MAAM,IACfC,EAAUpK,SAAS+F,iBAAiB,iCAExCqE,EAAQpE,SAAQ,SAAAuE,GACdA,EAAErI,aAAa,WAAY,OAdGmI,EAAArI,KAAA,EAiB5B3C,IAjB4B,KAAA,EAAA,OAkBlC+G,EAAQzB,cAAc,QAAQH,UAAY,GAlBR6F,EAAArI,KAAA,GAmB5B7C,EAAUwC,GAnBkB,KAAA,GAoB9ByI,GACFA,EAAQpE,SAAQ,SAAAuE,GACdA,EAAEhI,gBAAgB,eAGtBhD,EAAS4K,GACT9D,EAAOO,UAAUC,OAAO,UACxBT,EAAQQ,UAAUC,OAAO,UACzBP,EAAWM,UAAUC,OAAO,UAC5BN,EAAYK,UAAUC,OAAO,UAC7BL,EAAUI,UAAUC,OAAO,UAC3BJ,EAAaG,UAAUC,OAAO,iBAEvBtI,EAASoD,GAChBvD,GAAO,EAlC2B,KAAA,GAAA,IAAA,MAAA,OAAAiM,EAAA7H,UAAA0H,QAqCpCzD,EAAaC,iBAAiB,SAAS,WAGrCP,EAAOhD,MAAQ5E,EAASoD,UACjBpD,EAASoD,GAChB0E,EAAOO,UAAUC,OAAO,UACxBT,EAAQQ,UAAUC,OAAO,UACzBP,EAAWM,UAAUC,OAAO,UAC5BN,EAAYK,UAAUC,OAAO,UAC7BL,EAAUI,UAAUC,OAAO,UAC3BJ,EAAaG,UAAUC,OAAO,gBA/MvB,KAAA,EAAA,IAAA,MAAA,OAAAf,EAAAtD,UAAAgI,OAAH,OAAA,SAAAC,GAAA,OAAA9E,EAAAhD,MAAAC,KAAAC,YAAA,GAoNJnD,EAAI,WAAA,IAAAgL,EAAAlJ,EAAAC,IAAAC,MAAG,WAAMkE,GAAN,IAAA+E,EAAAC,EAAAC,EAAA5J,EAAA4G,EAAAiD,EAAA3H,EAAA4H,EAAAjL,EAAA,OAAA2B,IAAAI,MAAA,SAAAmJ,GAAA,OAAA,OAAAA,EAAAjJ,KAAAiJ,EAAAhJ,MAAA,KAAA,EAAA,GAKXzD,EAAW,IAEPoB,OAAOyD,SAAS+F,SAAS8B,SAAS,gBAP3B,CAAAD,EAAAhJ,KAAA,GAAA,MAAA,GAQH2I,EAAS,IAAIxD,gBAAgBxH,OAAOyD,SAAS8H,QAC7CN,EAAU,qBACVC,EAAU,sBACV5J,EAAM0J,EAAOQ,IAAI,UAAYR,EAAOS,IAAI,UAAYT,EAAOS,IAAI,YAC/DvD,EAAU7H,SAAS2E,cAAc,WAC3B,OAAR1D,IAAgBA,EAAIoK,OAbf,CAAAL,EAAAhJ,KAAA,GAAA,MAAA,OAeP1C,EAASuI,EAAS+C,GAfXI,EAAAhJ,KAAA,GAgBD3C,IAhBC,KAAA,GAiBPE,EAASsI,GACTvI,EAASuI,EAASgD,GAClB,IACQC,EAAWH,EAAOQ,IAAI,UAAY,SAAW,WACnDzM,EAAa4M,mBAAmBX,EAAOrJ,YACnC6B,EAAQ,GACN4H,EAAM,IAAIQ,eAChBjN,EAAO2C,EACP8J,EAAIS,KAAK,MAAOvK,GAAK,GACrB8J,EAAIU,OAASjK,EAAAC,IAAAC,MAAA,SAAAgK,IAAA,OAAAjK,IAAAI,MAAA,SAAA8J,GAAA,OAAA,OAAAA,EAAA5J,KAAA4J,EAAA3J,MAAA,KAAA,EAAA,OAEY,IAAnB+I,EAAIa,YACa,MAAfb,EAAIc,SACN1I,EAAQ4H,EAAIe,cAIC,WAAbhB,IACF3H,EAAQpE,EAAWoE,IATVwI,EAAA3J,KAAA,EAWL5C,EAAS+D,GAXJ,KAAA,EAAA,OAAAwI,EAAA3J,KAAA,EAYLvC,EAAKmG,GAZA,KAAA,EAaXrG,EAASsI,GACT7I,IAdW,KAAA,EAAA,IAAA,MAAA,OAAA2M,EAAAnJ,UAAAkJ,OAgBbX,EAAIgB,OACJ,MAAOC,IA3CFhB,EAAAhJ,KAAA,GAAA,MAAA,KAAA,GAAA,OA6CP1D,EAAO,GACDwB,EAAUf,EAAW4L,EAAOQ,IAAI,QAAUR,EAAOS,IAAI,QAAUtM,GACrEJ,EAAa4M,mBAAmBX,EAAOrJ,YACvChC,EAASuI,EAAS+C,GAhDXI,EAAAhJ,KAAA,GAiDD3C,IAjDC,KAAA,GAAA,OAkDPE,EAASsI,GACTvI,EAASuI,EAASgD,GAnDXG,EAAAhJ,KAAA,GAoDD5C,EAASU,GApDR,KAAA,GAAA,OAAAkL,EAAAhJ,KAAA,GAqDDvC,EAAKmG,GArDJ,KAAA,GAsDPrG,EAASsI,GACT7I,IAvDO,KAAA,GAAAgM,EAAAhJ,KAAA,GAAA,MAAA,KAAA,GA0DT1D,EAAO,GACPI,EAAa,GACbe,EAAKmG,GA5DI,KAAA,GAAA,IAAA,MAAA,OAAAoF,EAAAxI,UAAAyJ,OAAH,OAAA,SAAAC,GAAA,OAAAxB,EAAA/H,MAAAC,KAAAC,YAAA,GAiEV7C,SAAS0G,iBAAiB,SA7VE,SAAA7B,GAG1B,IAAMoE,EAAOtJ,OAAOyD,SAAS+F,SAAS5D,MAAM,KAAK,GAC3CtB,EAASY,EAAEZ,QAAUY,EAAEsH,WAC7B,GAAuB,MAAnBlI,EAAOmI,SAAmB1M,GAE1BuE,EAAOkE,aAAa,SACpBlE,EAAOuF,OAAS7J,OAAOyD,SAASoG,MAChC7J,OAAOyD,SAAS+F,WAAhB,IAAApJ,OAAiCkJ,EAFjC,kBAGAtJ,OAAOyD,SAAS+F,WAAalF,EAAOkF,UACpCxJ,OAAOyD,SAAS8H,SAAWjH,EAAOiH,OAClC,CACArG,EAAEI,iBACF,IAFAoH,EAGMC,EAAQ,GAHdC,koBAAAC,CAEe,IAAIrF,gBAAgBlD,EAAOiH,SAF1C,IAIA,IAAmCqB,EAAAE,MAAAJ,EAAAE,EAAAG,KAAAC,MAAA,CAAA,IAAAC,EAAAC,EAAAR,EAAAlJ,MAAA,GAAvB2B,EAAuB8H,EAAA,GAAlBzJ,EAAkByJ,EAAA,GACjCN,EAAMxH,GAAO3B,GALf,MAAA6I,GAAAO,EAAA1H,EAAAmH,GAAA,QAAAO,EAAAO,IAOA7F,QAAQC,UAAUoF,EAAO,GAAIrI,EAAOX,MACpC5D,GAAK,OA4UXC,OAAO+G,iBAAiB,YA9WP,WAGf,IAAMuC,EAAOtJ,OAAOyD,SAAS+F,SAAS5D,MAAM,KAAK,GAE/C5F,OAAOyD,SAAS+F,WAAiBF,IAAAA,OAAAA,mBAEjBqC,mBAAmB,IAAInE,gBAAgBxH,OAAOyD,SAAS8H,QAAQ5J,cAC/D5C,GACdgB,GAAK,MAwWXC,OAAO+G,iBAAiB,UAAU,WAChC/H,GAAO,KAITgB,OAAOoN,UAAUC,WAAU,WAGrBrO,EACFA,GAAO,EAGTe,GAAK"}