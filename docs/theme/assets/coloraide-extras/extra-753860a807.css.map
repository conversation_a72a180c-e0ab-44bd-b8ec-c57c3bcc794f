{"version": 3, "sources": ["_buttons.scss", "extra.css", "_inputs.scss", "_scroll.scss", "_loader.scss", "_swatch.scss", "_playground.scss", "_color_command.scss", "_notebook.scss"], "names": [], "mappings": "AAAA,MACE,uBAAA,qRACA,wBAAA,uQACA,sBAAA,wTACA,yBAAA,sPCIF,oCDCE,sCACE,SAAA,SACA,WAAA,MACA,QAAA,MAAA,IACA,aAAA,MACA,MAAA,2BACA,UAAA,IACA,WAAA,2BACA,cAAA,MACA,QAAA,GCEJ,0CDAI,4CACE,OAAA,QACA,QAAA,ECGN,8CDAI,gDACE,QAAA,GCGN,4CDAI,8CACG,SAAA,SACA,IAAA,MACA,KAAA,IACA,QAAA,MACA,WAAA,WACA,MAAA,OACA,OAAA,OACA,iBAAA,2BACA,gBAAA,OACA,WAAA,iBAAA,MACA,oBAAA,UAAA,YAAA,UACA,kBAAA,QAAA,UAAA,QACA,QAAA,GCKP,4DDFI,8DACE,mBAAA,4BAAA,WAAA,4BCMN,6DDHI,+DACE,mBAAA,6BAAA,WAAA,6BCON,8DACA,2DDLI,gEAAA,6DAEE,mBAAA,2BAAA,WAAA,2BCQN,8DACA,8DDNI,gEAAA,gEAEE,mBAAA,8BAAA,WAAA,8BCSN,2CDNI,6CACE,QAAA,KAMN,yBACE,YAAA,MCOF,mBC9EA,0BAEE,QAAA,EACA,QAAA,MACA,MAAA,KACA,WAAA,IAEA,OAAA,EACA,QAAA,cAAA,eACA,WAAA,KACA,WAAA,OACA,MAAA,wBACA,YAAA,wBAEA,UAAA,MACA,YAAA,4BAAA,CAAA,cAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UACA,sBAAA,OACA,YAAA,IACA,YAAA,IACA,UAAA,MACA,WAAA,wBAEA,OAAA,KACA,QAAA,EACA,OAAA,KACA,mBAAA,SAAA,gBAAA,SAAA,WAAA,SACA,OAAA,KAEA,mCAAA,0CACE,MAAA,wBACA,WAAA,wBD+EJ,8BCjFE,qCACE,MAAA,wBACA,WAAA,wBC9BJ,qBAEE,gBAAA,oCAAA,YACA,gBAAA,KAEA,2BACE,gBAAA,0BAAA,YAGF,wCACI,MAAA,MACA,OAAA,MAGJ,+CACE,iBAAA,YAGF,8CACE,iBAAA,oCAEA,qDACE,iBAAA,0BCtBN,SACI,SAAA,MACA,IAAA,EACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,QAAA,EACA,MAAA,KACA,OAAA,KACA,iBAAA,eACA,wBAAA,UAAA,gBAAA,UAEA,iBACI,SAAA,SACA,IAAA,IACA,KAAA,IACA,MAAA,KACA,OAAA,KACA,WAAA,MACA,YAAA,MACA,aAAA,MACA,aAAA,KACA,iBAAA,KACA,mBAAA,KACA,oBAAA,KACA,kBAAA,KACA,cAAA,IACA,kBAAA,KAAA,GAAA,OAAA,SAAA,UAAA,KAAA,GAAA,OAAA,SAEA,qBACI,SAAA,SACA,IAAA,IACA,MAAA,KACA,WAAA,KACA,YAAA,IACA,UAAA,KACA,WAAA,OAIR,kBACI,SAAA,SAEA,0BACI,MAAA,IACA,OAAA,IACA,WAAA,KACA,YAAA,KACA,aAAA,KAEA,8BACI,QAAA,KAMhB,wBACI,GACI,UAAA,UAEJ,KACI,UAAA,iBALR,gBACI,GACI,UAAA,UAEJ,KACI,UAAA,iBC9DR,MACE,sBAAA,gBACA,kBAAA,iBACA,sBAAA,gBACA,4BAAA,iBACA,eAAA,WAAA,CAAA,YAEA,mCACE,sBAAA,uBAGF,qCACE,kBAAA,2BACA,sBAAA,oBACA,sBAAA,oBACA,4BAAA,oBAIJ,QACE,QAAA,aACA,MAAA,OACA,OAAA,OACA,aAAA,KACA,YAAA,MACA,YAAA,EACA,eAAA,YACA,iBAAA,uBACA,iBAAA,qEAAA,CAAA,sEAAA,CAAA,qEAAA,CAAA,uEAIA,oBAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,MAAA,CAAA,OAAA,EACA,gBAAA,KAAA,KACA,cAAA,MACA,WAAA,KAAA,KAAA,KAAA,eACA,WAAA,IAAA,IAAA,KAEA,cACE,QAAA,EACA,UAAA,SAGF,oCACE,OAAA,QAIJ,cACE,QAAA,aACA,WAAA,WACA,MAAA,OACA,OAAA,OACA,OAAA,EACA,QAAA,EACA,eAAA,YACA,WAAA,8CACA,OAAA,IAAA,MAAA,2BACA,cAAA,MAGF,iBJwMA,+BItME,MAAA,KACA,OAAA,IACA,OAAA,EACA,cAAA,EAGF,eACE,WAAA,WACA,iBAAA,uBACA,iBAAA,qEAAA,CAAA,sEAAA,CAAA,qEAAA,CAAA,uEAIA,oBAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,MAAA,CAAA,OAAA,EACA,gBAAA,IAAA,IACA,OAAA,IAAA,MAAA,2BAEA,uBACE,oBAAA,EAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,KAAA,CAAA,MAAA,EACA,gBAAA,IAAA,IACA,WAAA,KAEA,6CAAA,2DAEE,MAAA,IACA,OAAA,IACA,OAAA,EACA,cAAA,EAGF,6BACE,UAAA,WAAA,kBAIJ,sCACE,UAAA,gBAAA,kBAIJ,mCACE,OAAA,IAAA,MAAA,iCACA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,wBCzGF,iBACE,SAAA,SACA,MAAA,KACA,OAAA,KACA,WAAA,IACA,SAAA,OAGF,iCACE,WAAA,IAGF,4BL4SA,iCK1SE,OAAA,EACA,SAAA,OAGF,qBACE,OAAA,mBACA,OAAA,EACA,eAAA,KAGF,yBACE,WAAA,IAGF,wBACE,cAAA,EAGF,gCACE,WAAA,gBAGF,sCACE,QAAA,KAGF,iDACE,aAAA,YAGF,mCACE,cAAA,EAGF,mBL4SA,2BK1SE,QAAA,KAGF,4CACE,QAAA,KCtDF,kBACE,OAAA,IAAA,EACA,YAAA,EAEA,+CACE,WAAA,IAAA,MAAA,qCACA,uBAAA,EACA,wBAAA,EAEA,mDACE,WAAA,ECVN,mBACE,OAAA,KPoXF,0BOjXA,0BAEE,QAAA,KDSF,wCACE,8BACE,aAAA,OACA,YAAA,OAEA,8CACE,cAAA", "file": "extra-753860a807.css", "sourcesContent": [":root {\n  --playground-edit-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z' /%3E%3C/svg%3E\");\n  --playground-share-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M14 16V13C10.39 13 7.81 14.43 6 17C6.72 13.33 8.94 9.73 14 9V6L19 11L14 16Z' /%3E%3C/svg%3E\");\n  --playground-run-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5C2,3.89 2.9,3 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z' /%3E%3C/svg%3E\");\n  --playground-cancel-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20 6.91L17.09 4L12 9.09L6.91 4L4 6.91L9.09 12L4 17.09L6.91 20L12 14.91L17.09 20L20 17.09L14.91 12L20 6.91Z' /%3E%3C/svg%3E\");\n}\n\n.playground,\n.notebook {\n  button:not(.md-clipboard) {\n    position: relative;\n    margin-top: 0.25em;\n    padding: 0.25em 1em;\n    padding-left: 2.5em;\n    color: var(--md-primary-bg-color);\n    font-size: 90%;\n    background: var(--md-primary-fg-color);\n    border-radius: px2rem(2px);\n    opacity: 0.8;\n\n    &:hover {\n      cursor: pointer;\n      opacity: 1;\n    }\n\n    &[disabled] {\n      opacity: 0.5;\n    }\n\n    &::before {\n       position: absolute;\n       top: 0.25em;\n       left: 1em;\n       display: block;\n       box-sizing: border-box;\n       width: 1.25em;\n       height: 1.25em;\n       background-color: var(--md-primary-bg-color);\n       background-size: 1.25em;\n       transition: background-color 125ms;\n       mask-repeat: no-repeat;\n       mask-size: contain;\n       content: \"\";\n    }\n\n    &.playground-edit::before {\n      mask-image: var(--playground-edit-icon);\n    }\n\n    &.playground-share::before {\n      mask-image: var(--playground-share-icon);\n    }\n\n    &#__notebook-submit::before,\n    &.playground-run::before {\n      mask-image: var(--playground-run-icon);\n    }\n\n    &#__notebook-cancel::before,\n    &.playground-cancel::before {\n      mask-image: var(--playground-cancel-icon);\n    }\n\n    &.hidden {\n      display: none;\n    }\n  }\n}\n\n// Special buttons in our toolbar\n.md-typeset .source-link {\n  margin-left: px2rem(8px);\n}\n", ":root {\n  --playground-edit-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z' /%3E%3C/svg%3E\");\n  --playground-share-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M14 16V13C10.39 13 7.81 14.43 6 17C6.72 13.33 8.94 9.73 14 9V6L19 11L14 16Z' /%3E%3C/svg%3E\");\n  --playground-run-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5C2,3.89 2.9,3 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z' /%3E%3C/svg%3E\");\n  --playground-cancel-icon: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M20 6.91L17.09 4L12 9.09L6.91 4L4 6.91L9.09 12L4 17.09L6.91 20L12 14.91L17.09 20L20 17.09L14.91 12L20 6.91Z' /%3E%3C/svg%3E\");\n}\n\n.playground button:not(.md-clipboard),\n.notebook button:not(.md-clipboard) {\n  position: relative;\n  margin-top: 0.25em;\n  padding: 0.25em 1em;\n  padding-left: 2.5em;\n  color: var(--md-primary-bg-color);\n  font-size: 90%;\n  background: var(--md-primary-fg-color);\n  border-radius: 0.1rem;\n  opacity: 0.8;\n}\n.playground button:not(.md-clipboard):hover,\n.notebook button:not(.md-clipboard):hover {\n  cursor: pointer;\n  opacity: 1;\n}\n.playground button:not(.md-clipboard)[disabled],\n.notebook button:not(.md-clipboard)[disabled] {\n  opacity: 0.5;\n}\n.playground button:not(.md-clipboard)::before,\n.notebook button:not(.md-clipboard)::before {\n  position: absolute;\n  top: 0.25em;\n  left: 1em;\n  display: block;\n  box-sizing: border-box;\n  width: 1.25em;\n  height: 1.25em;\n  background-color: var(--md-primary-bg-color);\n  background-size: 1.25em;\n  transition: background-color 125ms;\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n.playground button:not(.md-clipboard).playground-edit::before,\n.notebook button:not(.md-clipboard).playground-edit::before {\n  mask-image: var(--playground-edit-icon);\n}\n.playground button:not(.md-clipboard).playground-share::before,\n.notebook button:not(.md-clipboard).playground-share::before {\n  mask-image: var(--playground-share-icon);\n}\n.playground button:not(.md-clipboard)#__notebook-submit::before, .playground button:not(.md-clipboard).playground-run::before,\n.notebook button:not(.md-clipboard)#__notebook-submit::before,\n.notebook button:not(.md-clipboard).playground-run::before {\n  mask-image: var(--playground-run-icon);\n}\n.playground button:not(.md-clipboard)#__notebook-cancel::before, .playground button:not(.md-clipboard).playground-cancel::before,\n.notebook button:not(.md-clipboard)#__notebook-cancel::before,\n.notebook button:not(.md-clipboard).playground-cancel::before {\n  mask-image: var(--playground-cancel-icon);\n}\n.playground button:not(.md-clipboard).hidden,\n.notebook button:not(.md-clipboard).hidden {\n  display: none;\n}\n\n.md-typeset .source-link {\n  margin-left: 0.4rem;\n}\n\ntextarea#__notebook-input,\n.playground-inputs {\n  z-index: 1;\n  display: block;\n  width: 100%;\n  min-height: 3em;\n  margin: 0;\n  padding: 0.7720588235em 1.1764705882em;\n  overflow-x: auto;\n  overflow-y: hidden;\n  color: var(--md-code-fg-color);\n  caret-color: var(--md-code-fg-color);\n  font-size: 0.85em;\n  font-family: var(--md-code-font-family, _), SFMono-Regular, Consolas, Menlo, monospace;\n  font-feature-settings: \"kern\";\n  line-height: 1.4;\n  white-space: pre;\n  word-wrap: unset;\n  background: var(--md-code-bg-color);\n  border: none;\n  outline: none;\n  cursor: text;\n  appearance: textarea;\n  resize: none;\n}\ntextarea#__notebook-input::selection,\n.playground-inputs::selection {\n  color: var(--md-code-bg-color);\n  background: var(--md-code-fg-color);\n}\n\n.md-typeset textarea {\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  scrollbar-width: thin;\n}\n.md-typeset textarea:hover {\n  scrollbar-color: var(--md-accent-fg-color) transparent;\n}\n.md-typeset textarea::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-typeset textarea::-webkit-scrollbar-corner {\n  background-color: transparent;\n}\n.md-typeset textarea::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-typeset textarea::-webkit-scrollbar-thumb *:hover {\n  background-color: var(--md-accent-fg-color);\n}\n\n.loading {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 2;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(2px);\n}\n.loading .loader {\n  position: relative;\n  top: 50%;\n  left: 50%;\n  width: 80px;\n  height: 80px;\n  margin-top: -40px;\n  margin-left: -40px;\n  border-style: solid;\n  border-width: 10px;\n  border-top-color: rgb(0, 255, 255);\n  border-right-color: rgb(255, 255, 0);\n  border-bottom-color: rgb(0, 255, 0);\n  border-left-color: rgb(255, 0, 255);\n  border-radius: 50%;\n  animation: spin 2s linear infinite;\n}\n.loading .loader ~ div {\n  position: absolute;\n  top: 50%;\n  width: 100%;\n  margin-top: 40px;\n  font-weight: 800;\n  font-size: 1rem;\n  text-align: center;\n}\n.loading.relative {\n  position: absolute;\n}\n.loading.relative .loader {\n  width: 2em;\n  height: 2em;\n  margin-top: -1em;\n  margin-left: -1em;\n  border-width: 0.4em;\n}\n.loading.relative .loader ~ div {\n  display: none;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0);\n  }\n  100% {\n    transform: rotate(-360deg);\n  }\n}\n:root {\n  --swatch-border-color: hsl(0, 0%, 85%);\n  --swatch-bg-color: hsl(0, 0%, 100%);\n  --swatch-bg-alt-color: hsl(0, 0%, 90%);\n  --swatch-gamut-border-color: hsl(340 82% 52%);\n  --swatch-stops: transparent, transparent;\n}\n:root [data-md-color-scheme=slate] {\n  --swatch-border-color: hsla(232, 15%, 25%, 1);\n}\n:root [data-md-color-scheme=dracula] {\n  --swatch-bg-color: var(--md-default-bg-color);\n  --swatch-bg-alt-color: hsl(231deg 15% 20%);\n  --swatch-border-color: hsl(231deg 15% 25%);\n  --swatch-gamut-border-color: hsl(326, 100%, 74%);\n}\n\n.swatch {\n  display: inline-block;\n  width: 1.25em;\n  height: 1.25em;\n  margin-right: 0.2em;\n  margin-left: 0.25em;\n  line-height: 0;\n  vertical-align: text-bottom;\n  background-color: var(--swatch-bg-color);\n  background-image: linear-gradient(45deg, var(--swatch-bg-alt-color) 25%, transparent 25%), linear-gradient(-45deg, var(--swatch-bg-alt-color) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, var(--swatch-bg-alt-color) 75%), linear-gradient(-45deg, transparent 75%, var(--swatch-bg-alt-color) 75%);\n  background-position: 0 0, 0 0.24em, 0.24em -0.24em, -0.24em 0px;\n  background-size: 0.5em 0.5em;\n  border-radius: 100px;\n  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.3);\n  transition: all 0.5s ease;\n}\n.swatch:hover {\n  z-index: 2;\n  transform: scale(2);\n}\n.swatch:not(.swatch-gradient):hover {\n  cursor: pointer;\n}\n\n.swatch-color {\n  display: inline-block;\n  box-sizing: border-box;\n  width: 1.25em;\n  height: 1.25em;\n  margin: 0;\n  padding: 0;\n  vertical-align: text-bottom;\n  background: linear-gradient(to right, var(--swatch-stops));\n  border: 2px solid var(--swatch-border-color);\n  border-radius: 100px;\n}\n\n.swatch-gradient,\n.swatch-gradient .swatch-color {\n  width: 100%;\n  height: 3em;\n  margin: 0;\n  border-radius: 0;\n}\n\ndiv.swatch-bar {\n  box-sizing: border-box;\n  background-color: var(--swatch-bg-color);\n  background-image: linear-gradient(45deg, var(--swatch-bg-alt-color) 25%, transparent 25%), linear-gradient(-45deg, var(--swatch-bg-alt-color) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, var(--swatch-bg-alt-color) 75%), linear-gradient(-45deg, transparent 75%, var(--swatch-bg-alt-color) 75%);\n  background-position: 0 0, 0 0.49em, 0.49em -0.49em, -0.49em 0px;\n  background-size: 1em 1em;\n  border: 2px solid var(--swatch-bg-alt-color);\n}\ndiv.swatch-bar .swatch {\n  background-position: 0 0, 0 0.5em, 0.5em -0.5em, -0.5em 0px;\n  background-size: 1em 1em;\n  box-shadow: none;\n}\ndiv.swatch-bar .swatch:not(.swatch-gradient), div.swatch-bar .swatch:not(.swatch-gradient) .swatch-color {\n  width: 3em;\n  height: 3em;\n  margin: 0;\n  border-radius: 0;\n}\ndiv.swatch-bar .swatch:hover {\n  transform: scale(1.2) translateY(-0.2em);\n}\ndiv.swatch-bar .swatch-gradient:hover {\n  transform: scale(1.02, 1.2) translateY(-0.2em);\n}\n\n.swatch.out-of-gamut .swatch-color {\n  border: 1px solid var(--swatch-gamut-border-color);\n  box-shadow: inset 0 0 0 1px var(--md-code-bg-color);\n}\n\n.playground-code {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  margin-top: 2px;\n  overflow: hidden;\n}\n\n.playground-code .highlight code {\n  min-height: 3em;\n}\n\n.playground-code .highlight,\n.playground-code .highlight code {\n  margin: 0;\n  overflow: hidden;\n}\n\n.playground-code pre {\n  height: calc(100% + 0.2rem);\n  margin: 0;\n  pointer-events: none;\n}\n\n.playground-results code {\n  min-height: 3em;\n}\n\n.playground-results pre {\n  margin-bottom: 0;\n}\n\n.playground-results .swatch-bar {\n  min-height: calc(3em + 4px);\n}\n\n.playground-results .swatch-bar:empty {\n  display: none;\n}\n\n.playground-results .swatch-bar:empty + .highlight {\n  border-color: transparent;\n}\n\n.playground-results .color-command {\n  margin-bottom: 0;\n}\n\n.hidden .highlight,\n.hidden .playground-inputs {\n  display: none;\n}\n\n.playground-code:not(.hidden) div.highlight {\n  display: none;\n}\n\ndiv.color-command {\n  margin: 1em 0;\n  line-height: 0;\n}\ndiv.color-command .highlight:not(:first-child) {\n  border-top: 1px solid var(--md-default-fg-color--lightest);\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\ndiv.color-command .highlight:not(:first-child) pre {\n  margin-top: 0;\n}\n\n@media screen and (max-width: 44.9375em) {\n  .md-typeset > div.color-command {\n    margin-right: -0.8rem;\n    margin-left: -0.8rem;\n  }\n  .md-typeset > div.color-command .highlight code {\n    border-radius: 0;\n  }\n}\n#__notebook-source {\n  height: 100%;\n}\n\n#__notebook-source.hidden,\n#__notebook-render.hidden {\n  display: none;\n}", "textarea#__notebook-input,\n.playground-inputs {\n  z-index: 1;\n  display: block;\n  width: 100%;\n  min-height: 3em;\n\n  margin: 0;\n  padding: .7720588235em 1.1764705882em;\n  overflow-x: auto;\n  overflow-y: hidden;\n  color: var(--md-code-fg-color);\n  caret-color: var(--md-code-fg-color);\n\n  font-size: .85em;\n  font-family: var(--md-code-font-family,_),SFMono-Regular,Consolas,Menlo,monospace;\n  font-feature-settings: \"kern\";\n  line-height: 1.4;\n  white-space: pre;\n  word-wrap: unset;\n  background: var(--md-code-bg-color);\n\n  border: none;\n  outline: none;\n  cursor: text;\n  appearance: textarea;\n  resize: none;\n\n  &::selection {\n    color: var(--md-code-bg-color);\n    background: var(--md-code-fg-color);\n  }\n}\n", ".md-typeset textarea {\n\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  scrollbar-width: thin;\n\n  &:hover {\n    scrollbar-color: var(--md-accent-fg-color) transparent;\n  }\n\n  &::-webkit-scrollbar {\n      width: px2rem(4px);\n      height: px2rem(4px);\n  }\n\n  &::-webkit-scrollbar-corner {\n    background-color: transparent;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    background-color: var(--md-default-fg-color--lighter);\n\n    *:hover {\n      background-color: var(--md-accent-fg-color);\n    }\n  }\n}\n", ".loading {\n    position: fixed;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: 2;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0,0,0,.2);\n    backdrop-filter: blur(2px);\n\n    .loader {\n        position: relative;\n        top: 50%;\n        left: 50%;\n        width: 80px;\n        height: 80px;\n        margin-top: -40px;\n        margin-left: -40px;\n        border-style: solid;\n        border-width: 10px;\n        border-top-color: rgb(0, 255, 255);\n        border-right-color: rgb(255, 255, 0);\n        border-bottom-color: rgb(0, 255, 0);\n        border-left-color: rgb(255, 0, 255);\n        border-radius: 50%;\n        animation: spin 2s linear infinite;\n\n        ~ div {\n            position: absolute;\n            top: 50%;\n            width: 100%;\n            margin-top: 40px;\n            font-weight: 800;\n            font-size: px2rem(20px);\n            text-align: center;\n        }\n    }\n\n    &.relative {\n        position: absolute;\n\n        .loader {\n            width: 2em;\n            height: 2em;\n            margin-top: -1em;\n            margin-left: -1em;\n            border-width: 0.4em;\n\n            ~ div {\n                display:none;\n            }\n        }\n    }\n}\n\n@keyframes spin {\n    0% {\n        transform: rotate(0);\n    }\n    100% {\n        transform: rotate(-360deg);\n    }\n}\n", ":root {\n  --swatch-border-color: hsl(0, 0%, 85%);\n  --swatch-bg-color: hsl(0, 0%, 100%);\n  --swatch-bg-alt-color: hsl(0, 0%, 90%);\n  --swatch-gamut-border-color: hsl(340 82% 52%);\n  --swatch-stops: transparent, transparent;\n\n  [data-md-color-scheme=\"slate\"] {\n    --swatch-border-color: hsla(232, 15%, 25%, 1);\n  }\n\n  [data-md-color-scheme=\"dracula\"] {\n    --swatch-bg-color: var(--md-default-bg-color);\n    --swatch-bg-alt-color: hsl(231deg 15% 20%);\n    --swatch-border-color: hsl(231deg 15% 25%);\n    --swatch-gamut-border-color: hsl(326, 100%, 74%);\n  }\n}\n\n.swatch {\n  display: inline-block;\n  width: 1.25em;\n  height: 1.25em;\n  margin-right: 0.2em;\n  margin-left: 0.25em;\n  line-height: 0;\n  vertical-align: text-bottom;\n  background-color: var(--swatch-bg-color);\n  background-image: linear-gradient(45deg, var(--swatch-bg-alt-color) 25%, transparent 25%),\n                    linear-gradient(-45deg, var(--swatch-bg-alt-color) 25%, transparent 25%),\n                    linear-gradient(45deg, transparent 75%, var(--swatch-bg-alt-color) 75%),\n                    linear-gradient(-45deg, transparent 75%, var(--swatch-bg-alt-color) 75%);\n  background-position: 0 0, 0 0.24em, 0.24em -0.24em, -0.24em 0px;\n  background-size: 0.5em 0.5em;\n  border-radius: 100px;\n  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, .3);\n  transition: all 0.5s ease;\n\n  &:hover {\n    z-index: 2;\n    transform: scale(2);\n  }\n\n  &:not(.swatch-gradient):hover {\n    cursor: pointer;\n  }\n}\n\n.swatch-color {\n  display: inline-block;\n  box-sizing: border-box;\n  width: 1.25em;\n  height: 1.25em;\n  margin: 0;\n  padding: 0;\n  vertical-align: text-bottom;\n  background: linear-gradient(to right, var(--swatch-stops));\n  border: 2px solid var(--swatch-border-color);\n  border-radius: 100px;\n}\n\n.swatch-gradient,\n.swatch-gradient .swatch-color {\n  width: 100%;\n  height: 3em;\n  margin: 0;\n  border-radius: 0;\n}\n\ndiv.swatch-bar {\n  box-sizing: border-box;\n  background-color: var(--swatch-bg-color);\n  background-image: linear-gradient(45deg, var(--swatch-bg-alt-color) 25%, transparent 25%),\n                    linear-gradient(-45deg, var(--swatch-bg-alt-color) 25%, transparent 25%),\n                    linear-gradient(45deg, transparent 75%, var(--swatch-bg-alt-color) 75%),\n                    linear-gradient(-45deg, transparent 75%, var(--swatch-bg-alt-color) 75%);\n  background-position: 0 0, 0 0.49em, 0.49em -0.49em, -0.49em 0px;\n  background-size: 1em 1em;\n  border: 2px solid var(--swatch-bg-alt-color);\n\n  .swatch {\n    background-position: 0 0, 0 0.50em, 0.50em -0.50em, -0.50em 0px;\n    background-size: 1em 1em;\n    box-shadow: none;\n\n    &:not(.swatch-gradient),\n    &:not(.swatch-gradient) .swatch-color {\n      width: 3em;\n      height: 3em;\n      margin: 0;\n      border-radius: 0;\n    }\n\n    &:hover {\n      transform: scale(1.2) translateY(-0.2em);\n    }\n  }\n\n  .swatch-gradient:hover {\n    transform: scale(1.02, 1.2) translateY(-0.2em);\n  }\n}\n\n.swatch.out-of-gamut .swatch-color {\n  border: 1px solid var(--swatch-gamut-border-color);\n  box-shadow: inset 0 0 0 1px var(--md-code-bg-color);\n}\n", ".playground-code {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  margin-top: 2px;\n  overflow: hidden;\n}\n\n.playground-code .highlight code {\n  min-height: 3em;\n}\n\n.playground-code .highlight,\n.playground-code .highlight code {\n  margin: 0;\n  overflow: hidden;\n}\n\n.playground-code pre {\n  height: calc(100% + px2rem(4px));\n  margin: 0;\n  pointer-events: none;\n}\n\n.playground-results code {\n  min-height: 3em;\n}\n\n.playground-results pre {\n  margin-bottom: 0;\n}\n\n.playground-results .swatch-bar {\n  min-height: calc(3em + 4px);\n}\n\n.playground-results .swatch-bar:empty {\n  display: none;\n}\n\n.playground-results .swatch-bar:empty + .highlight {\n  border-color: transparent;\n}\n\n.playground-results .color-command {\n  margin-bottom: 0;\n}\n\n.hidden .highlight,\n.hidden .playground-inputs {\n  display: none;\n}\n\n.playground-code:not(.hidden) div.highlight {\n  display: none;\n}\n", "div.color-command {\n  margin: 1em 0;\n  line-height: 0;\n\n  .highlight:not(:first-child) {\n    border-top: 1px solid var(--md-default-fg-color--lightest);\n    border-top-left-radius: 0;\n    border-top-right-radius: 0;\n\n    pre {\n      margin-top: 0;\n    }\n  }\n}\n\n@media screen and (max-width: 44.9375em) {\n  .md-typeset > div.color-command {\n    margin-right:  px2rem(-16px);\n    margin-left: px2rem(-16px);\n\n    .highlight code {\n      border-radius: 0;\n    }\n  }\n}\n", "#__notebook-source {\n  height: 100%;\n}\n\n#__notebook-source.hidden,\n#__notebook-render.hidden {\n  display: none;\n}\n"]}