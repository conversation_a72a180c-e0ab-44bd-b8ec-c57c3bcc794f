name: deploy

on:
  push:
    tags:
    - '*'

jobs:

  documents:
    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.9]

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Setup Node
      uses: actions/setup-node@v2
      with:
        node-version: '16'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools build
        python -m pip install -r requirements/docs.txt
        npm install --legacy-peer-deps
    - name: Deploy documents
      run: |
        git config user.name facelessuser
        git config user.email "${{ secrets.GH_EMAIL }}"
        git remote add gh-token "https://${{ secrets.GH_TOKEN }}@github.com/facelessuser/coloraide.git"
        git fetch gh-token && git fetch gh-token gh-pages:gh-pages
        python tools/buildwheel.py
        npm run build
        python -m mkdocs gh-deploy -v --clean --remote-name gh-token
        git push gh-token gh-pages

  pypi:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Package
        run: |
          pip install --upgrade build
          python -m build -s -w
      - name: Publish
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          user: __token__
          password: ${{ secrets.PYPI_PWD }}
