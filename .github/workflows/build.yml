name: build

on:
  push:
    branches:
      - 'main'
    tags:
    - '**'
  pull_request:
    branches:
    - '**'

jobs:
  tests:
    strategy:
      fail-fast: false
      max-parallel: 4
      matrix:
        platform: [ubuntu-latest, windows-latest]
        python-version: [3.7, 3.8, 3.9, '3.10']
        include:
          - python-version: 3.7
            tox-env: py37
          - python-version: 3.8
            tox-env: py38
          - python-version: 3.9
            tox-env: py39
          - python-version: '3.10'
            tox-env: py310

    env:
      TOXENV: ${{ matrix.tox-env }}

    runs-on: ${{ matrix.platform }}

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      if: "!endsWith(matrix.python-version, '-dev')"
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Set up development Python ${{ matrix.python-version }}
      if: endsWith(matrix.python-version, '-dev')
      uses: deadsnakes/action@v1.0.0
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox coverage codecov
    - name: Test
      run: |
        python -m tox
    - name: Upload Results
      if: success()
      uses: codecov/codecov-action@v1
      with:
        file: ./coverage.xml
        flags: unittests
        name: ${{ matrix.platform }}-${{ matrix.tox-env }}
        fail_ci_if_error: false

  lint:
    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.9]

    env:
      TOXENV: lint

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
    - name: Lint
      run: |
        python -m tox

  documents:
    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.9]

    env:
      TOXENV: documents

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Setup Node
      uses: actions/setup-node@v2
      with:
        node-version: '16'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox build
        npm install --legacy-peer-deps
    - name: Install Aspell
      run: |
        sudo apt-get install aspell aspell-en
    - name: Build documents
      run: |
        python tools/buildwheel.py
        npm run build
        python -m tox
